import { BaseSearch, ImageStrapi, Tag } from '@/types/common';
import Category from '@/types/Category';

export interface SearchProduct extends BaseSearch {
    categoryId?: number;
    tagIds?: number[];
    documentIds?: string[];
    priceRange?: number;
}

export default interface Product extends Tag {
    keywords: string;
    display_order: number;
    price: number;
    sale_price: number;
    description: string;
    content: string;
    image: ImageStrapi;
    categories: Category[];
    tags: Tag[];
}

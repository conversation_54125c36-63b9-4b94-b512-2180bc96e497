export interface PaginationData {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
}

export interface ResponseList<T> {
    data: T[] | null;
    meta?: {
        pagination: PaginationData;
    };
}

export interface ItemLink {
    id: number;
    name: string;
    link: string;
}

export interface Tag {
    id: number;
    documentId: string;
    title: string;
    slug: string;
    createdAt: string;
    updatedAt?: string;
}

export interface BaseSearch {
    page?: number;
    limit?: number;
    q?: string;
    tagId?: number;
    differentId?: number;
    ids?: number[];
    slug?: string;
    sortBy?: number;
}

export interface LabelValue {
    label: string;
    value: number;
}

export interface ImageStrapi {
    url: string;
    width: number;
    height: number;
}

export interface VdoVideo {
    otp: string;
    playbackInfo: string;
}

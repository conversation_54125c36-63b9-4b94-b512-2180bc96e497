import Category from './Category';
import { BaseSearch, ImageStrapi, LabelValue, Tag } from './common';
import Product from '@/types/Product';

export interface SearchCourse extends BaseSearch {
    categoryId?: number;
    highlight?: boolean;
    isOffline?: string;
    tagIds?: number[];
    documentIds?: string[];
    displayList?: boolean;
    priceRange?: number;
}

export interface Lesson {
    id: number;
    title: string;
    duration?: string;
    video?: string;
    content: string;
    is_preview: boolean;
}
export interface Chapter {
    id: number;
    title: string;
    duration?: string;
    Lessions?: Lesson[];
}

export const CourseSorts: LabelValue[] = [
    {
        value: 0,
        label: 'Độ phổ biến',
    },
    {
        value: 1,
        label: 'Mới nhất',
    },
    {
        value: 2,
        label: 'Gi<PERSON> tăng dần',
    },
    {
        value: 3,
        label: 'Giá giảm dần',
    },
    {
        value: 4,
        label: 'Tiêu đề A->Z',
    },
    {
        value: 5,
        label: 'Tiêu đề Z->A',
    },
];

export default interface Course extends Tag {
    keywords: string;
    display_order: number;
    is_offline: boolean;
    price: number;
    sale_price: number;
    description: string;
    video: string;
    publishedAt: string;
    image: ImageStrapi;
    highlight: boolean;
    categories: Category[];
    chapters: Chapter[];
    learns: Tag[];
    tag: Tag;
    tags: Tag[];
    products: Product[];
}

export interface OrderData {
    id: string;
    price: number;
    quantity: number;
    isCourse: boolean;
}

export interface ShippingData {
    name: string;
    phone: string;
    province: string;
    district: string;
    ward: string;
    address: string;
    comment?: string;
}

export default interface Order {
    documentId?: string;
    id?: number;
    user_id: number;
    price: number;
    payment_method_id: number;
    code: string;
    approved: boolean;
    data: string | OrderData[];
    shipping: string | ShippingData;
    createdAt?: string;
}

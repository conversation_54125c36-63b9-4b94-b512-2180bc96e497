import User from '@/types/User';
import { randomNumber } from '@/utils';
import { AUTH_KEYS } from '@/utils/constants';
import Cookies from 'js-cookie';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export interface AuthStore {
    user: User | null;
    showLogin: boolean;
    orderCode: string;
    setUser: (user: User | null) => void;
    setShowLogin: (showLogin: boolean) => void;
    setOrderCode: (user: User | null) => void;
}

export const useAuthStore = create<AuthStore>()(
    devtools(
        persist(
            (set) => ({
                user: null,
                showLogin: false,
                orderCode: '',
                setUser: (user: User | null) => {
                    set({ user });
                    if (user) {
                        Cookies.set(AUTH_KEYS.USER_COOKIE, JSON.stringify(user));
                        set({ orderCode: `HVY${10000 + +(user?.id ?? '0')}${randomNumber(100, 999)}` });
                    } else {
                        Cookies.remove(AUTH_KEYS.USER_COOKIE);
                        set({ orderCode: '' });
                    }
                },
                setShowLogin: (showLogin: boolean) => set({ showLogin }),
                setOrderCode: (user: User | null) =>
                    set({ orderCode: `HVY${10000 + +(user?.id ?? '0')}${randomNumber(100, 999)}` }),
            }),
            {
                name: 'auth-storage',
            }
        )
    )
);

import { AUTH_KEYS } from '@/utils/constants';
import Cookies from 'js-cookie';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { LabelValue } from '@/types/common';

export interface CartStore {
    courseIds: string[];
    products: LabelValue[];
    setCourseIds: (courseIds: string[]) => void;
    setProducts: (products: LabelValue[]) => void;
}

export const useCartStore = create<CartStore>()(
    devtools(
        persist(
            (set) => ({
                courseIds: [],
                products: [],
                setCourseIds: (courseIds: string[]) => {
                    set({ courseIds });
                    Cookies.set(AUTH_KEYS.CART, JSON.stringify(courseIds));
                },
                setProducts: (products: LabelValue[]) => {
                    set({ products });
                    Cookies.set(AUTH_KEYS.CART_PRODUCT, JSON.stringify(products));
                },
            }),
            {
                name: 'cart-storage',
            }
        )
    )
);

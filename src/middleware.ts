import { type NextRequest, NextResponse } from 'next/server';
import User from './types/User';
import { isAuthRoute } from './utils';
import { AUTH_KEYS } from './utils/constants';

export function middleware(request: NextRequest) {
    const currentUser = request.cookies.get(AUTH_KEYS.USER_COOKIE)?.value;
    const pathname = request.nextUrl.pathname;
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set(AUTH_KEYS.HEADER_PATHNAME, pathname);
    requestHeaders.set(AUTH_KEYS.CART, request.cookies.get(AUTH_KEYS.CART)?.value ?? '[]');
    if (currentUser) {
        const user = JSON.parse(currentUser) as User;
        requestHeaders.set(AUTH_KEYS.HEADER_USER_ID, user.id.toString());
        requestHeaders.set(AUTH_KEYS.HEADER_ACCESS_TOKEN, user.jwt ?? '');
    } else {
        requestHeaders.set(AUTH_KEYS.HEADER_USER_ID, '0');
        requestHeaders.set(AUTH_KEYS.HEADER_ACCESS_TOKEN, '');
        if (
            !pathname.startsWith('/not-found') &&
            !pathname.startsWith('/manifest.webmanifest') &&
            !pathname.startsWith('/_next') &&
            !pathname.startsWith('/assets') &&
            isAuthRoute(pathname)
        ) {
            return NextResponse.rewrite(new URL('/not-found', request.url));
        }
    }
    return NextResponse.next({
        request: {
            headers: requestHeaders,
        },
    });
}

export const config = {
    matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};

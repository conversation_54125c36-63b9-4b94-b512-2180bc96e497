import { fetchAPI } from '@/services/fetchApi';
import UserLogin from '@/types/UserLogin';
import { mutationAPI } from '@/services/mutationApi';
import { API_BEARER_MUTATION } from '@/utils/constants';

export const getUserLogins = async (userId: number) => {
    if (userId && userId > 0) {
        const params = new URLSearchParams({
            'filters[user_id][$eq]': userId.toString(),
            _t: new Date().getTime().toString(),
        });
        const data = await fetchAPI(`/user-logins?${params.toString()}`);
        return data.data as UserLogin[] | [];
    }
    return [];
};

export const addUserLogin = async (data: UserLogin) => {
    const response = await mutationAPI('/user-logins', { data }, API_BEARER_MUTATION);
    return response as { data: UserLogin };
};

export const updateUserLogin = async (id: string, data: UserLogin) => {
    return await mutationAPI(`/user-logins/${id}`, { data }, API_BEARER_MUTATION, 'PUT');
};

export const deleteUserLogin = async (id: string) => {
    return await mutationAPI(`/user-logins/${id}`, {}, API_BEARER_MUTATION, 'DELETE');
};

import Order from '@/types/Order';
import { API_BEARER_MUTATION } from '@/utils/constants';
import { fetchAPI } from './fetchApi';
import { mutationAPI } from './mutationApi';

export const getOrders = async (userId: number) => {
    if (userId && userId > 0) {
        const params = new URLSearchParams({
            'filters[user_id][$eq]': userId.toString(),
            'sort': 'createdAt:desc',
        });
        const data = await fetchAPI(`/orders?${params.toString()}`);
        return data.data as Order[] | [];
    }
    return [];
};

export const addOrder = async (data: Order) => {
    const response = await mutationAPI('/orders', { data }, API_BEARER_MUTATION);
    return response as { data: Order };
};

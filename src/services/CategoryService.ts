import Category from '@/types/Category';
import { fetchAPI } from './fetchApi';

export const getCategories = async (isCourse: boolean = true, id: number = 0) => {
    const params = new URLSearchParams({
        populate: '*',
        'filters[is_course][$eq]': isCourse ? 'true' : 'false',
    });
    if (id > 0) {
        params.append('filters[id][$eq]', id.toString());
    }
    const data = await fetchAPI(`/categories?${params.toString()}`);
    return data.data as Category[] | null;
};

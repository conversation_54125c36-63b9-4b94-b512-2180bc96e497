import { API_BEARER_READ, API_URL } from '@/utils/constants';
import qs from 'qs';

export async function fetchAPI(path: string, urlParamsObject = {}, options = {}) {
    try {
        const mergedOptions = {
            next: { revalidate: 60 },
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${API_BEARER_READ}`,
            },
            ...options,
        };
        const queryString = qs.stringify(urlParamsObject);
        const requestUrl = `${API_URL}/api${path}${queryString ? `?${queryString}` : ''}`;
        const response = await fetch(requestUrl, mergedOptions);
        return await response.json();
    } catch (error) {
        console.error('Error fetch API:', error);
        //throw new Error("");
    }
}

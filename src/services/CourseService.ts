import Course, { SearchCourse } from '@/types/Course';
import { ResponseList } from '@/types/common';
import { isEmpty } from 'lodash';
import { fetchAPI } from './fetchApi';

export const getCourses = async (param: SearchCourse) => {
    const params = new URLSearchParams({
        'fields[0]': 'title',
        'fields[1]': 'slug',
        'fields[2]': 'description',
        'fields[3]': 'publishedAt',
        'fields[4]': 'price',
        'fields[5]': 'sale_price',
        'fields[6]': 'createdAt',
        'fields[7]': 'updatedAt',
        'populate[image][fields][1]': 'url',
        'populate[tag][fields][1]': 'title',
    });
    if (param.differentId) {
        params.append('filters[id][$lt]', param.differentId.toString());
    }
    if (param.q) {
        params.append('filters[title][$containsi]', param.q);
    }
    if (param.categoryId && param.categoryId > 0) {
        params.append('filters[categories][id][$eq]', param.categoryId.toString());
    }
    if (param.tagId && param.tagId > 0) {
        params.append('filters[tags][id][$eq]', param.tagId.toString());
    }
    if (!isEmpty(param.tagIds)) {
        param.tagIds?.forEach((tagId, index) => {
            params.append(`filters[tags][id][$in][${index}]`, tagId.toString());
        });
    }
    if (!isEmpty(param.documentIds)) {
        param.documentIds?.forEach((documentId, index) => {
            params.append(`filters[documentId][$in][${index}]`, documentId);
        });
    }
    if (!isEmpty(param.ids)) {
        param.ids?.forEach((id, index) => {
            params.append(`filters[id][$in][${index}]`, id.toString());
        });
    }
    if (param.highlight) {
        params.append('filters[highlight][$eq]', 'true');
    }
    if (param.isOffline && param.isOffline !== '0') {
        params.append('filters[is_offline][$eq]', param.isOffline === '1' ? 'true' : 'false');
    }
    if ((param.priceRange ?? -1) >= 0) {
        if (param.priceRange === 0) params.append('filters[sale_price][$eq]', '0');
        else {
            params.append('filters[sale_price][$between]', ((param.priceRange! - 1) * 500000).toString());
            params.append('filters[sale_price][$between]', (param.priceRange! * 500000).toString());
        }
    }
    if (param.sortBy) {
        switch (param.sortBy) {
            case 1:
                params.append('sort', 'createdAt:desc');
                break;
            case 2:
                params.append('sort', 'sale_price:asc');
                break;
            case 3:
                params.append('sort', 'sale_price:desc');
                break;
            case 4:
                params.append('sort', 'title:asc');
                break;
            case 5:
                params.append('sort', 'title:desc');
                break;
            default:
                params.append('sort', 'display_order:asc');
                break;
        }
    } else {
        params.append('sort', 'display_order:asc');
    }
    if (param.page) {
        params.append('pagination[page]', param.page.toString());
    }
    if (param.limit) {
        params.append('pagination[pageSize]', param.limit.toString());
    }
    const data = await fetchAPI(`/courses?${params.toString()}`);
    return data as ResponseList<Course>;
};

export const getCourse = async (id: string) => {
    const params = new URLSearchParams({
        'populate[0]': 'chapters.Lessions',
        'populate[1]': 'learns',
        'populate[2]': 'tag',
        'populate[3]': 'tags',
        'populate[4]': 'image',
        'populate[5]': 'products',
    });
    const data = await fetchAPI(`/courses/${id}?${params.toString()}`);
    return data?.data as Course | null;
};

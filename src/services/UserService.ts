import User from '@/types/User';
import { API_BEARER_MUTATION } from '@/utils/constants';
import { mutationAPI } from './mutationApi';

export const registerUser = async (data: User) => {
    const response = await mutationAPI('/auth/local/register', data, API_BEARER_MUTATION);
    return response as { jwt: string; user: User };
};

export const login = async (identifier: string, password: string) => {
    const response = await mutationAPI('/auth/local', { identifier, password }, API_BEARER_MUTATION);
    return response as { jwt: string; user: User };
};

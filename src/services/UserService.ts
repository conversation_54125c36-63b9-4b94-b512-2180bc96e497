import User from '@/types/User';
import { API_BEARER_MUTATION, WEB_URL } from '@/utils/constants';
import { mutationAPI } from './mutationApi';

export const registerUser = async (data: User) => {
    const response = await mutationAPI('/auth/local/register', data, API_BEARER_MUTATION);
    return response as { jwt: string; user: User };
};

export const login = async (identifier: string, password: string) => {
    const response = await mutationAPI('/auth/local', { identifier, password }, API_BEARER_MUTATION);
    return response as { jwt: string; user: User };
};

export const loginLocal = async (email: string, password: string) => {
    const response = await mutationAPI('/users/login', { email, password }, '', 'POST', WEB_URL);
    return response as { jwt: string; user: User; message: string; success: boolean };
};

export const logoutLocal = async (id: number, token: string) => {
    return await mutationAPI('/users/logout', { id, token }, '', 'POST', WEB_URL);
};

import User from '@/types/User';
import { API_BEARER_MUTATION } from '@/utils/constants';
import { mutationAPI } from './mutationApi';

export const registerUser = async (data: User) => {
    const response = await mutationAPI('/auth/local/register', data, API_BEARER_MUTATION);
    return response as { jwt: string; user: User };
};

export const login = async (identifier: string, password: string) => {
    const response = await mutationAPI('/auth/local', { identifier, password }, API_BEARER_MUTATION);
    return response as { jwt: string; user: User };
};

export const loginLocal = async (email: string, password: string) => {
    try {
        const response = await fetch('/api/users/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password }),
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data as { jwt: string; user: User; message: string; success: boolean };
    } catch (error) {
        console.error('Local login error:', error);
        throw error;
    }
};

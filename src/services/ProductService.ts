import Product, { SearchProduct } from '@/types/Product';
import { ResponseList } from '@/types/common';
import { isEmpty } from 'lodash';
import { fetchAPI } from './fetchApi';

export const getProducts = async (param: SearchProduct) => {
    const params = new URLSearchParams({
        'fields[0]': 'title',
        'fields[1]': 'slug',
        'fields[2]': 'description',
        'fields[3]': 'price',
        'fields[4]': 'sale_price',
        'fields[5]': 'createdAt',
        'fields[6]': 'updatedAt',
        'populate[image][fields][1]': 'url',
    });
    if (param.differentId) {
        params.append('filters[id][$lt]', param.differentId.toString());
    }
    if (param.q) {
        params.append('filters[title][$containsi]', param.q);
    }
    if (param.categoryId && param.categoryId > 0) {
        params.append('filters[categories][id][$eq]', param.categoryId.toString());
    }
    if (param.tagId && param.tagId > 0) {
        params.append('filters[tags][id][$eq]', param.tagId.toString());
    }
    if (!isEmpty(param.tagIds)) {
        param.tagIds?.forEach((tagId, index) => {
            params.append(`filters[tags][id][$in][${index}]`, tagId.toString());
        });
    }
    if (!isEmpty(param.documentIds)) {
        param.documentIds?.forEach((documentId, index) => {
            params.append(`filters[documentId][$in][${index}]`, documentId);
        });
    }
    if ((param.priceRange ?? -1) >= 0) {
        if (param.priceRange! === 0) params.append('filters[sale_price][$eq]', '0');
        else {
            params.append('filters[sale_price][$between]', ((param.priceRange! - 1) * 500000).toString());
            params.append('filters[sale_price][$between]', (param.priceRange! * 500000).toString());
        }
    }
    if (param.sortBy) {
        switch (param.sortBy) {
            case 1:
                params.append('sort', 'createdAt:desc');
                break;
            case 2:
                params.append('sort', 'sale_price:asc');
                break;
            case 3:
                params.append('sort', 'sale_price:desc');
                break;
            case 4:
                params.append('sort', 'title:asc');
                break;
            case 5:
                params.append('sort', 'title:desc');
                break;
            default:
                params.append('sort', 'display_order:asc');
                break;
        }
    } else {
        params.append('sort', 'display_order:asc');
    }
    if (param.page) {
        params.append('pagination[page]', param.page.toString());
    }
    if (param.limit) {
        params.append('pagination[pageSize]', param.limit.toString());
    }
    const data = await fetchAPI(`/products?${params.toString()}`);
    return data as ResponseList<Product>;
};

export const getProduct = async (id: string) => {
    const data = await fetchAPI(`/products/${id}?populate[0]=tags&populate[1]=image&populate[2]=categories`);
    return data.data as Product | null;
};

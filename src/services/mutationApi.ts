import { API_URL } from '@/utils/constants';
import { includes } from 'lodash';

export async function mutationAPI(path: string, data: object, token: string, method = 'POST', baseUrl = API_URL) {
    try {
        const response = await fetch(`${baseUrl}/api${path}`, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: token ? `Bearer ${token}` : '',
            },
            method,
            body: includes(['POST', 'PUT'], method) ? JSON.stringify(data) : undefined,
        });
        return includes(['POST', 'PUT'], method) ? await response.json() : true;
    } catch (error) {
        console.error('Error fetch API:', error);
        //throw new Error("");
    }
}

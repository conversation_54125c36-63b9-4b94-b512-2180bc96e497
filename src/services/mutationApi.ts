import { API_URL } from '@/utils/constants';

export async function mutationAPI(path: string, data: object, token: string, method = 'POST', baseUrl = API_URL) {
    try {
        const response = await fetch(`${baseUrl}/api${path}`, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: token ? `Bearer ${token}` : '',
            },
            method,
            body: method === 'POST' ? JSON.stringify(data) : undefined,
        });
        return method === 'POST' ? await response.json() : true;
    } catch (error) {
        console.error('Error fetch API:', error);
        //throw new Error("");
    }
}

import Article, { SearchArticle } from '@/types/Article';
import { ResponseList } from '@/types/common';
import { fetchAPI } from './fetchApi';

export const getArticles = async (param: SearchArticle) => {
    const params = new URLSearchParams({
        'fields[0]': 'title',
        'fields[1]': 'slug',
        'fields[2]': 'description',
        'fields[3]': 'publishedAt',
        'fields[4]': 'createdAt',
        'fields[5]': 'updatedAt',
        'populate[image][fields][1]': 'url',
        'filters[is_page][$eq]': param.isPage ? 'true' : 'false',
        sort: 'id:desc',
    });
    if (param.isPage) {
        params.append('fields[6]', 'content');
    }
    if (param.differentId) {
        params.append('filters[id][$lt]', param.differentId.toString());
    }
    if (param.q) {
        params.append('filters[title][$containsi]', param.q);
    }
    if (param.slug) {
        params.append('filters[slug][$eq]', param.slug);
    }
    if (param.tagId) {
        params.append('filters[tags][id][$eq]', param.tagId.toString());
    }
    if (param.page) {
        params.append('pagination[page]', param.page.toString());
    }
    if (param.limit) {
        params.append('pagination[pageSize]', param.limit.toString());
    }
    const data = await fetchAPI(`/articles?${params.toString()}`);
    return data as ResponseList<Article>;
};

export const getArticle = async (id: string) => {
    const data = await fetchAPI(`/articles/${id}?populate[0]=tags&populate[1]=image`);
    return data.data as Article | null;
};

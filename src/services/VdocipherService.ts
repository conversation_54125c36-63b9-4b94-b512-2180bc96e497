import { VdoVideo } from '@/types/common';
import { API_KEY_VDO } from '@/utils/constants';

export const getVideoOtp = async (videoId: string, ttl = 300) => {
    try {
        const response = await fetch(`https://dev.vdocipher.com/api/videos/${videoId}/otp`, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Apisecret ${API_KEY_VDO}`,
            },
            method: 'POST',
            body: JSON.stringify({ data: { ttl } }),
        });
        const resp = await response.json();
        return resp as VdoVideo;
    } catch (error) {
        console.error('Error fetch VDO API:', error);
        return null;
        //throw new Error("");
    }
};

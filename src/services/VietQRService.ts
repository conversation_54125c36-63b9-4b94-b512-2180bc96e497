import { VietQRRequest, VietQRResponse } from '@/types/VietQR';
import { API_VIETQR_CLIENT, API_VIETQR_KEY, API_VIETQR_URL } from '@/utils/constants';

export const getQRImage = async (data: VietQRRequest) => {
    try {
        const response = await fetch(API_VIETQR_URL, {
            headers: {
                'Content-Type': 'application/json',
                'x-client-id': API_VIETQR_CLIENT,
                'x-api-key': API_VIETQR_KEY,
            },
            method: 'POST',
            body: JSON.stringify(data),
        });
        const resp = await response.json();
        return resp as VietQRResponse;
    } catch (error) {
        console.error('Error fetch VietQR API:', error);
        return { code: '01' };
        //throw new Error("");
    }
};

import UserCourse from '@/types/UserCourse';
import { API_BEARER_MUTATION } from '@/utils/constants';
import { fetchAPI } from './fetchApi';
import { mutationAPI } from './mutationApi';

export const getUserCourses = async (userId: number, courseId?: number) => {
    if (userId && userId > 0) {
        const params = new URLSearchParams({
            'filters[user_id][$eq]': userId.toString(),
        });
        if (courseId && courseId > 0) {
            params.append('filters[course_id][$eq]', courseId.toString());
        }
        const data = await fetchAPI(`/user-courses?${params.toString()}`);
        return data.data as UserCourse[] | [];
    }
    return [];
};

export const addUserCourse = async (data: UserCourse) => {
    const response = await mutationAPI('/user-courses', { data }, API_BEARER_MUTATION);
    return response as { data: UserCourse };
};

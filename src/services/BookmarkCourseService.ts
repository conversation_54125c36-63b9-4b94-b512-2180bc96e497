import BookmarkCourse from '@/types/BookmarkCourse';
import { API_BEARER_MUTATION } from '@/utils/constants';
import { fetchAPI } from './fetchApi';
import { mutationAPI } from './mutationApi';

export const getBookmarkCourses = async (userId: number) => {
    if (userId && userId > 0) {
        const data = await fetchAPI(`/bookmark-courses?filters[user_id][$eq]=${userId}`);
        return data.data as BookmarkCourse[] | [];
    }
    return [];
};

export const addBookmarkCourse = async (data: BookmarkCourse) => {
    const response = await mutationAPI('/bookmark-courses', { data }, API_BEARER_MUTATION);
    return response as { data: BookmarkCourse };
};

export const deleteBookmarkCourse = async (id: string) => {
    const response = await mutationAPI(`/bookmark-courses/${id}`, {}, API_BEARER_MUTATION, 'DELETE');
    return response;
};

import { NextApiRequest, NextApiResponse } from 'next';
import { login } from '@/services/UserService';
import { addUserLogin, getUserLogins, updateUserLogin } from '@/services/UserLoginService';
import { isEmpty } from 'lodash';

function getRealIP(req: NextApiRequest): string {
    const forwarded = req.headers['x-forwarded-for'];
    const realIP = req.headers['x-real-ip'];
    const cfConnectingIP = req.headers['cf-connecting-ip'];
    if (forwarded) {
        return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0].trim();
    }
    if (realIP) {
        return Array.isArray(realIP) ? realIP[0] : realIP;
    }
    if (cfConnectingIP) {
        return Array.isArray(cfConnectingIP) ? cfConnectingIP[0] : cfConnectingIP;
    }
    return req.socket.remoteAddress ?? '';
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
    try {
        await handleLogin(req, res);
    } catch (error) {
        console.error('Login API Error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}

async function handleLogin(req: NextApiRequest, res: NextApiResponse) {
    const { email, password, identifier } = req.body;
    const loginIdentifier = email || identifier;
    if (!loginIdentifier || !password) {
        return res.status(400).json({ error: 'Email and password are required.' });
    }
    try {
        const response = await login(loginIdentifier, password);
        if (response.user) {
            const userLoginData = { user_id: response.user.id, ip: getRealIP(req), token: response.jwt };
            const userLogins = await getUserLogins(response.user.id);
            if (isEmpty(userLogins)) {
                await addUserLogin(userLoginData);
            } else {
                await updateUserLogin(userLogins[0].documentId!, userLoginData);
            }
            res.status(200).json({
                user: response.user,
                jwt: response.jwt,
            });
        } else {
            res.status(200).json({
                user: null,
                jwt: null,
            });
        }
    } catch (error) {
        console.error('Login Error:', error);
        res.status(200).json({
            user: null,
            jwt: null,
        });
    }
}

import { NextApiRequest, NextApiResponse } from 'next';
import { login } from '../../../services/UserService';

/**
 * Get real IP address from request headers
 */
function getRealIP(req: NextApiRequest): string {
    const forwarded = req.headers['x-forwarded-for'];
    const realIP = req.headers['x-real-ip'];
    const cfConnectingIP = req.headers['cf-connecting-ip'];

    if (forwarded) {
        // x-forwarded-for can contain multiple IPs, get the first one
        return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0].trim();
    }

    if (realIP) {
        return Array.isArray(realIP) ? realIP[0] : realIP;
    }

    if (cfConnectingIP) {
        return Array.isArray(cfConnectingIP) ? cfConnectingIP[0] : cfConnectingIP;
    }

    // Fallback to connection remote address
    return req.socket.remoteAddress || 'unknown';
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Get and log real IP address
    const realIP = getRealIP(req);
    console.log('Login attempt from IP:', realIP);

    // Only allow POST method
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    try {
        await handleLogin(req, res);
    } catch (error) {
        console.error('Login API Error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
}

/**
 * POST /api/users/login - User login
 */
async function handleLogin(req: NextApiRequest, res: NextApiResponse) {
    const { email, password, identifier } = req.body;
    const realIP = getRealIP(req);

    // Use email as identifier if provided, otherwise use identifier field
    const loginIdentifier = email || identifier;

    console.log(`Login attempt - IP: ${realIP}, Identifier: ${loginIdentifier || 'not provided'}`);
    
    // Validate required fields
    if (!loginIdentifier || !password) {
        console.log(`❌ Login validation failed - IP: ${realIP}, Reason: Missing required fields`);
        return res.status(400).json({
            error: 'Missing required fields',
            message: 'Email/identifier and password are required',
            required: ['email or identifier', 'password']
        });
    }
    
    // Validate email format if email is provided
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ 
                error: 'Invalid email format',
                message: 'Please provide a valid email address'
            });
        }
    }
    
    // Validate password
    if (typeof password !== 'string' || password.trim().length === 0) {
        return res.status(400).json({ 
            error: 'Invalid password',
            message: 'Password cannot be empty'
        });
    }
    
    try {
        // Call the UserService login method
        const response = await login(loginIdentifier.trim(), password);
        
        // Check if login was successful
        if (!response) {
            console.log(`❌ Login failed - IP: ${realIP}, Identifier: ${loginIdentifier}, Reason: No response from service`);
            return res.status(401).json({
                error: 'Login failed',
                message: 'Invalid credentials or service unavailable'
            });
        }

        // Check if response contains user and jwt
        if (!response.user || !response.jwt) {
            console.log(`❌ Login failed - IP: ${realIP}, Identifier: ${loginIdentifier}, Reason: Invalid response structure`);
            return res.status(401).json({
                error: 'Authentication failed',
                message: 'Invalid credentials'
            });
        }
        
        // Successful login
        console.log(`✅ Login successful - IP: ${realIP}, User: ${response.user.email || response.user.username}, ID: ${response.user.id}`);

        res.status(200).json({
            message: 'Login successful',
            user: response.user,
            jwt: response.jwt,
            success: true
        });
        
    } catch (error) {
        console.error(`❌ UserService login error - IP: ${realIP}, Identifier: ${loginIdentifier}:`, error);

        // Handle specific error cases
        if (error instanceof Error) {
            const errorMessage = error.message.toLowerCase();

            // Check for common authentication errors
            if (errorMessage.includes('unauthorized') ||
                errorMessage.includes('invalid') ||
                errorMessage.includes('credentials')) {
                console.log(`❌ Login failed - IP: ${realIP}, Identifier: ${loginIdentifier}, Reason: Invalid credentials`);
                return res.status(401).json({
                    error: 'Authentication failed',
                    message: 'Invalid email or password'
                });
            }
            
            // Check for network/service errors
            if (errorMessage.includes('network') || 
                errorMessage.includes('fetch') || 
                errorMessage.includes('connection')) {
                return res.status(503).json({ 
                    error: 'Service unavailable',
                    message: 'Authentication service is temporarily unavailable'
                });
            }
        }
        
        // Generic error response
        res.status(500).json({ 
            error: 'Login failed',
            message: 'An error occurred during login. Please try again.'
        });
    }
}

import { NextApiRequest, NextApiResponse } from 'next';
import { login } from '../../../services/UserService';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Only allow POST method
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

    try {
        await handleLogin(req, res);
    } catch (error) {
        console.error('Login API Error:', error);
        res.status(500).json({ 
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
}

/**
 * POST /api/users/login - User login
 */
async function handleLogin(req: NextApiRequest, res: NextApiResponse) {
    const { email, password, identifier } = req.body;
    
    // Use email as identifier if provided, otherwise use identifier field
    const loginIdentifier = email || identifier;
    
    // Validate required fields
    if (!loginIdentifier || !password) {
        return res.status(400).json({ 
            error: 'Missing required fields',
            message: 'Email/identifier and password are required',
            required: ['email or identifier', 'password']
        });
    }
    
    // Validate email format if email is provided
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({ 
                error: 'Invalid email format',
                message: 'Please provide a valid email address'
            });
        }
    }
    
    // Validate password
    if (typeof password !== 'string' || password.trim().length === 0) {
        return res.status(400).json({ 
            error: 'Invalid password',
            message: 'Password cannot be empty'
        });
    }
    
    try {
        // Call the UserService login method
        const response = await login(loginIdentifier.trim(), password);
        
        // Check if login was successful
        if (!response) {
            return res.status(401).json({ 
                error: 'Login failed',
                message: 'Invalid credentials or service unavailable'
            });
        }
        
        // Check if response contains user and jwt
        if (!response.user || !response.jwt) {
            return res.status(401).json({ 
                error: 'Authentication failed',
                message: 'Invalid credentials'
            });
        }
        
        // Successful login
        res.status(200).json({
            message: 'Login successful',
            user: response.user,
            jwt: response.jwt,
            success: true
        });
        
    } catch (error) {
        console.error('UserService login error:', error);
        
        // Handle specific error cases
        if (error instanceof Error) {
            const errorMessage = error.message.toLowerCase();
            
            // Check for common authentication errors
            if (errorMessage.includes('unauthorized') || 
                errorMessage.includes('invalid') || 
                errorMessage.includes('credentials')) {
                return res.status(401).json({ 
                    error: 'Authentication failed',
                    message: 'Invalid email or password'
                });
            }
            
            // Check for network/service errors
            if (errorMessage.includes('network') || 
                errorMessage.includes('fetch') || 
                errorMessage.includes('connection')) {
                return res.status(503).json({ 
                    error: 'Service unavailable',
                    message: 'Authentication service is temporarily unavailable'
                });
            }
        }
        
        // Generic error response
        res.status(500).json({ 
            error: 'Login failed',
            message: 'An error occurred during login. Please try again.'
        });
    }
}

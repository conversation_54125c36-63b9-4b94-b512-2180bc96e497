import { NextApiRequest, NextApiResponse } from 'next';
import { deleteUserLogin, getUserLogins } from '@/services/UserLoginService';
import { isEmpty } from 'lodash';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
    try {
        await handleLogout(req, res);
    } catch (error) {
        console.error('Login API Error:', error);
        res.status(500).json({
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : 'Unknown error',
        });
    }
}

async function handleLogout(req: NextApiRequest, res: NextApiResponse) {
    const { id, token } = req.body;
    try {
        const userId  = id ? parseInt(id, 10) : 0;
        if (userId > 0 && !!token) {
            const userLogins = await getUserLogins(userId);
            if (!isEmpty(userLogins)) {
                const userLogin = userLogins[0];
                if (userLogin.token === token) {
                    await deleteUserLogin(userLogin.documentId!);
                }
            }
        }
        res.status(200).json({});
    } catch (error) {
        console.error('Logout Error:', error);
        res.status(200).json({});
    }
}

import { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';
import { COMMON_MESSAGE, EMAIL_FROM, EMAIL_HOST, EMAIL_PASSWORD, EMAIL_PORT } from '@/utils/constants';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: COMMON_MESSAGE.ERROR_MESSAGE });
    }
    const { name, email, subject, message } = req.body;
    if (!name || !email || !subject || !message) {
        return res.status(400).json({ message: '<PERSON><PERSON><PERSON> trường bắt buộc không được bỏ trống' });
    }
    if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) || email === EMAIL_FROM) {
        return res.status(400).json({ message: '<PERSON><PERSON> không hợp lệ' });
    }
    try {
        const transporter = nodemailer.createTransport({
            host: EMAIL_HOST,
            port: EMAIL_PORT,
            secure: false,
            auth: {
                user: EMAIL_FROM,
                pass: EMAIL_PASSWORD,
            },
        });

        await transporter.sendMail({
            from: `"Học viện Yêu B & G" <${EMAIL_FROM}>`,
            to: EMAIL_FROM,
            subject: `Contact Form B & G: ${subject}`,
            text: `${name} - ${email} - ${subject} - ${message}`,
            html: `<p>Name: <strong>${name}</strong></p><p>Email: <strong>${email}</strong></p><p>Subject: <strong>${subject}</strong></p><p>Message: <strong>${message}</strong></p>`,
        });
        return res.status(200).json({ message: 'Xin chân thành cảm ơn, chúng tôi sẽ phản hồi bạn trong vòng 24h' });
    } catch (error) {
        console.error('Error sending email:', error);
        return res.status(500).json({ message: COMMON_MESSAGE.ERROR_MESSAGE });
    }
}

import ItemText from '@/components/common/ItemText';
import AccordionLessons from '@/components/course/AccordionLessons';
import { getCourse } from '@/services/CourseService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { getUserCourses } from '@/services/UserCourseService';
import { getVideoOtp } from '@/services/VdocipherService';
import { VdoVideo } from '@/types/common';
import Course, { Lesson } from '@/types/Course';
import { GlobalConfig } from '@/types/GlobalConfig';
import UserCourse from '@/types/UserCourse';
import { AUTH_KEYS, COURSE_ROUTE } from '@/utils/constants';
import { find, findIndex, isEmpty, last, split } from 'lodash';
import { marked } from 'marked';
import { headers } from 'next/headers';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// Utility to get client IP from headers
function getClientIp() {
    const headersList = headers();
    // Helper to extract IPv4 from ::ffff:IPv4 or plain IPv4
    const extractIPv4 = (ip: string) => {
        const ipv4Match = ip.match(/(?:(?:\d{1,3}\.){3}\d{1,3})/);
        return ipv4Match ? ipv4Match[0] : null;
    };
    const xForwardedFor = headersList.get('x-forwarded-for');
    if (xForwardedFor) {
        const ips = xForwardedFor.split(',').map(ip => ip.trim());
        for (const ip of ips) {
            const ipv4 = extractIPv4(ip);
            if (ipv4 && ipv4 !== '127.0.0.1') return ipv4;
        }
    }
    const xRealIp = headersList.get('x-real-ip');
    if (xRealIp) {
        const ipv4 = extractIPv4(xRealIp);
        if (ipv4 && ipv4 !== '127.0.0.1') return ipv4;
    }
    const cfConnectingIp = headersList.get('cf-connecting-ip');
    if (cfConnectingIp) {
        const ipv4 = extractIPv4(cfConnectingIp);
        if (ipv4 && ipv4 !== '127.0.0.1') return ipv4;
    }
    const fastlyClientIp = headersList.get('fastly-client-ip');
    if (fastlyClientIp) {
        const ipv4 = extractIPv4(fastlyClientIp);
        if (ipv4 && ipv4 !== '127.0.0.1') return ipv4;
    }
    const trueClientIp = headersList.get('true-client-ip');
    if (trueClientIp) {
        const ipv4 = extractIPv4(trueClientIp);
        if (ipv4 && ipv4 !== '127.0.0.1') return ipv4;
    }
    return null;
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
    const id = last(split(params.slug, '-'));
    let data: Course | null = null;
    let config: GlobalConfig | null = null;
    if (id) {
        [data, config] = await Promise.all([getCourse(id), getGlobalConfig()]);
    }
    const siteTitle = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: data?.title ? `${data?.title} - ${siteTitle}` : siteTitle,
        robots: 'noindex, nofollow',
    };
}

export default async function LessonDetail({
    params,
}: {
    params: { slug: string; chapterId: string; lessonId: string };
}) {
    const ip = getClientIp();
    console.log(ip);
    let data: Course | null = null;
    let lesson: Lesson | undefined = undefined;
    let userCourses: UserCourse[] = [];
    let otpVideo: VdoVideo | null = null;
    const headersList = headers();
    const userId = +(headersList.get(AUTH_KEYS.HEADER_USER_ID) ?? '0');
    const id = last(split(params.slug, '-'));
    if (id) {
        data = await getCourse(id);
        if(data) userCourses = await getUserCourses(userId, data.id);
        if (data && !isEmpty(userCourses) && userCourses[0].approved) {
            let chapter = find(data.chapters, { id: +params.chapterId });
            if (!chapter) chapter = data.chapters[0];
            if (chapter) {
                lesson = find(chapter.Lessions, { id: +params.lessonId });
                if (!lesson) lesson = chapter.Lessions?.[0];
                if (lesson) {
                    lesson.content = lesson.content ? await marked.parse(lesson.content) : '';
                    if (lesson.video) {
                        otpVideo = await getVideoOtp(lesson.video);
                    }
                } else notFound();
            } else notFound();
        } else notFound();
    } else notFound();

    const getNextPrevLesson = () => {
        const chapterIndex = findIndex(data.chapters, { id: +params.chapterId });
        if (chapterIndex > -1) {
            const lessonIndex = findIndex(data.chapters[chapterIndex].Lessions, { id: +params.lessonId });
            if (lessonIndex > -1) {
                let prevChapterId = 0,
                    nextChapterId = 0,
                    prevLessonId = 0,
                    nextLessonId = 0;
                if (lessonIndex === 0) {
                    if (chapterIndex > 0) {
                        prevChapterId = data.chapters[chapterIndex - 1].id;
                        const lIndex = data.chapters[chapterIndex - 1].Lessions!.length - 1;
                        prevLessonId = data.chapters[chapterIndex - 1].Lessions![lIndex].id;
                    }
                } else {
                    prevChapterId = data.chapters[chapterIndex].id;
                    prevLessonId = data.chapters[chapterIndex].Lessions![lessonIndex - 1].id;
                }
                if (lessonIndex === data.chapters[chapterIndex].Lessions!.length - 1) {
                    if (chapterIndex < data.chapters.length - 1) {
                        nextChapterId = data.chapters[chapterIndex + 1].id;
                        nextLessonId = data.chapters[chapterIndex + 1].Lessions![0].id;
                    }
                } else {
                    nextChapterId = data.chapters[chapterIndex].id;
                    nextLessonId = data.chapters[chapterIndex].Lessions![lessonIndex + 1].id;
                }
                if (prevLessonId > 0 || nextLessonId > 0) {
                    return (
                        <div className="bg-color-extra2 ptb--15 overflow-hidden">
                            <div className="rbt-button-group">
                                {prevLessonId > 0 && (
                                    <Link
                                        href={`/hoc-bai/${data.slug}-${data.documentId}/${prevChapterId}/${prevLessonId}`}
                                        className="rbt-btn icon-hover icon-hover-left btn-md bg-primary-opacity"
                                    >
                                        <span className="btn-icon">
                                            <i className="feather-arrow-left" />
                                        </span>
                                        <span className="btn-text">Bài trước</span>
                                    </Link>
                                )}
                                {nextLessonId > 0 && (
                                    <Link
                                        href={`/hoc-bai/${data.slug}-${data.documentId}/${nextChapterId}/${nextLessonId}`}
                                        className="rbt-btn icon-hover btn-md"
                                    >
                                        <span className="btn-text">Tiếp theo</span>
                                        <span className="btn-icon">
                                            <i className="feather-arrow-right" />
                                        </span>
                                    </Link>
                                )}
                            </div>
                        </div>
                    );
                }
            }
        }
        return <></>;
    };

    return data && lesson ? (
        <div className="rbt-lesson-area bg-color-white">
            <div className="rbt-lesson-content-wrapper">
                <div className="rbt-lesson-leftsidebar">
                    <AccordionLessons
                        slug={`${data.slug}-${data.documentId}`}
                        chapterId={+params.chapterId}
                        lessonId={+params.lessonId}
                        chapters={data.chapters}
                    />
                </div>
                <div className="rbt-lesson-rightsidebar overflow-hidden">
                    <div className="lesson-top-bar">
                        <div className="lesson-top-left">
                            <div className="rbt-lesson-toggle">
                                <Link
                                    href={`${COURSE_ROUTE}/${data.slug}-${data.documentId}`}
                                    className="lesson-toggle-active btn-round-white-opacity"
                                >
                                    <i className="feather-arrow-left" />
                                </Link>
                            </div>
                            <h5>{data.title}</h5>
                        </div>
                        <div className="lesson-top-right">
                            <div className="rbt-btn-close">
                                <Link
                                    href={`${COURSE_ROUTE}/${data.slug}-${data.documentId}`}
                                    className="rbt-round-btn"
                                >
                                    <i className="feather-x" />
                                </Link>
                            </div>
                        </div>
                    </div>
                    <div className="inner">
                        <div className="content">
                            <div className="section-title">
                                <h4>{lesson.title}</h4>
                                {otpVideo && (
                                    <iframe
                                        src={`https://player.vdocipher.com/v2/?otp=${otpVideo.otp}&playbackInfo=${otpVideo.playbackInfo}`}
                                        style={{ border: 0, height: '360px', width: '100%' }}
                                        allowFullScreen={true}
                                        allow="encrypted-media"
                                    />
                                )}
                                <ItemText text={lesson.content} className={['lesson-content']} />
                            </div>
                        </div>
                    </div>
                    {getNextPrevLesson()}
                </div>
            </div>
        </div>
    ) : (
        <></>
    );
}

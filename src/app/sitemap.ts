import { getArticles } from '@/services/ArticleService';
import { getCategories } from '@/services/CategoryService';
import { getCourses } from '@/services/CourseService';
import { getTags } from '@/services/TagService';
import { ARTICLE_ROUTE, COURSE_ROUTE, PRODUCT_ROUTE, WEB_URL } from '@/utils/constants';
import type { MetadataRoute } from 'next';
import { getProducts } from '@/services/ProductService';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
    const lastModified = new Date();
    const sitemaps: MetadataRoute.Sitemap = [
        {
            url: WEB_URL,
            lastModified,
            changeFrequency: 'weekly',
            priority: 0.9,
        },
        {
            url: `${WEB_URL}/ve-chung-toi`,
            lastModified,
            changeFrequency: 'monthly',
            priority: 0.7,
        },
        {
            url: `${WEB_URL}/lien-he`,
            lastModified,
            changeFrequency: 'monthly',
            priority: 0.7,
        },
        {
            url: `${WEB_URL}/the-loai`,
            lastModified,
            changeFrequency: 'monthly',
            priority: 0.7,
        },
        {
            url: `${WEB_URL}/goi-dang-ky`,
            lastModified,
            changeFrequency: 'monthly',
            priority: 0.7,
        },
        {
            url: `${WEB_URL}/faqs`,
            lastModified,
            changeFrequency: 'monthly',
            priority: 0.7,
        },
        // {
        //     url: `${WEB_URL}/dang-ky-lam-giang-vien`,
        //     lastModified,
        //     changeFrequency: 'monthly',
        //     priority: 0.7,
        // },
        {
            url: `${WEB_URL}/huong-dan`,
            lastModified,
            changeFrequency: 'monthly',
            priority: 0.7,
        },
        {
            url: `${WEB_URL}${ARTICLE_ROUTE}`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.7,
        },
        {
            url: `${WEB_URL}${COURSE_ROUTE}`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        },
        {
            url: `${WEB_URL}/khoa-hoc-noi-bat`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        },
        {
            url: `${WEB_URL}/khoa-hoc-online-truc-tuyen`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        },
        {
            url: `${WEB_URL}/khoa-hoc-tai-trung-tam`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        },
        {
            url: `${WEB_URL}${PRODUCT_ROUTE}`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        },
    ];
    const [courseCategories, productCategories, tags, articles, pages, courses, products] = await Promise.all([
        getCategories(),
        getCategories(false),
        getTags(),
        getArticles({ limit: 1000, isPage: false }),
        getArticles({ limit: 1000, isPage: true }),
        getCourses({ limit: 1000 }),
        getProducts({ limit: 1000 }),
    ]);
    (courseCategories ?? []).forEach((item) => {
        sitemaps.push({
            url: `${WEB_URL}/the-loai/${item.slug}-${item.id}`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        });
    });
    (productCategories ?? []).forEach((item) => {
        sitemaps.push({
            url: `${WEB_URL}/chuyen-muc/${item.slug}-${item.id}`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        });
    });
    (tags ?? []).forEach((item) => {
        sitemaps.push({
            url: `${WEB_URL}/tag/${item.slug}-${item.id}`,
            lastModified,
            changeFrequency: 'daily',
            priority: 0.8,
        });
    });
    (articles.data ?? []).forEach((item) => {
        sitemaps.push({
            url: `${WEB_URL}${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`,
            lastModified: item.updatedAt ?? item.createdAt,
            changeFrequency: 'daily',
            priority: 0.7,
        });
    });
    (pages.data ?? []).forEach((item) => {
        sitemaps.push({
            url: `${WEB_URL}/page/${item.slug}`,
            lastModified: item.updatedAt ?? item.createdAt,
            changeFrequency: 'monthly',
            priority: 0.6,
        });
    });
    (courses.data ?? []).forEach((item) => {
        sitemaps.push({
            url: `${WEB_URL}${COURSE_ROUTE}/${item.slug}-${item.documentId}`,
            lastModified: item.updatedAt ?? item.createdAt,
            changeFrequency: 'daily',
            priority: 0.8,
        });
    });
    (products.data ?? []).forEach((item) => {
        sitemaps.push({
            url: `${WEB_URL}${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`,
            lastModified: item.updatedAt ?? item.createdAt,
            changeFrequency: 'daily',
            priority: 0.8,
        });
    });
    return sitemaps;
}

import { getGlobalConfig } from '@/services/SingleTypeService';
import { isAuthRoute } from '@/utils';
import { API_URL, AUTH_KEYS } from '@/utils/constants';
import { headers } from 'next/headers';
import React from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

export async function generateMetadata() {
    const config = await getGlobalConfig();
    const headersList = headers();
    const pathname = headersList.get(AUTH_KEYS.HEADER_PATHNAME) ?? '';
    const title = config?.siteName ?? 'Học viện yêu B & G';
    const description = config?.siteDesc ?? 'Học viện Yêu Boys & Girls';
    const image = `${API_URL}${config?.logoFooter.url}`;
    return {
        title,
        description,
        keywords: config?.keywords ?? '',
        robots: isAuthRoute(pathname) ? 'noindex, nofollow' : 'index, follow',
        openGraph: {
            title,
            description,
            images: [
                {
                    url: image,
                    width: config?.logoFooter.width,
                    height: config?.logoFooter.height,
                    alt: title,
                },
            ],
            type: 'website',
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            images: [image],
        },
    };
}

export default async function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <head>
                <link rel="stylesheet" href="/assets/css/vendor/bootstrap.min.css" />
                <link rel="stylesheet" href="/assets/css/vendor/slick.css" />
                <link rel="stylesheet" href="/assets/css/vendor/slick-theme.css" />
                <link rel="stylesheet" href="/assets/css/plugins/sal.css" />
                <link rel="stylesheet" href="/assets/css/plugins/feather.css" />
                <link rel="stylesheet" href="/assets/css/plugins/fontawesome.min.css" />
                {/* <link rel="stylesheet" href="/assets/css/plugins/euclid-circulara.css" /> */}
                <link rel="stylesheet" href="/assets/css/plugins/cerebrisans.css" />
                <link rel="stylesheet" href="/assets/css/plugins/swiper.css" />
                {/* <link rel="stylesheet" href="/assets/css/plugins/magnify.css" /> */}
                <link rel="stylesheet" href="/assets/css/plugins/odometer.css" />
                <link rel="stylesheet" href="/assets/css/plugins/animation.css" />
                <link rel="stylesheet" href="/assets/css/plugins/bootstrap-select.min.css" />
                <link rel="stylesheet" href="/assets/css/plugins/jquery-ui.css" />
                <link rel="stylesheet" href="/assets/css/plugins/magnigy-popup.min.css" />
                <link rel="stylesheet" href="/assets/css/plugins/plyr.css" />
                <link rel="stylesheet" href="/assets/css/style.css" />
                <meta name="apple-mobile-web-app-title" content="Học viện yêu B & G" />
            </head>
            <body className="rbt-header-sticky">
                {children}
                <ToastContainer autoClose={2000} />
            </body>
        </html>
    );
}

import type { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
    return {
        name: '<PERSON><PERSON><PERSON> viện yêu B & G',
        short_name: '<PERSON><PERSON><PERSON> viện yêu B & G',
        description: '<PERSON><PERSON><PERSON> viện Yêu Boys & Girls',
        start_url: '/',
        display: 'standalone',
        background_color: '#ffffff',
        theme_color: '#000000',
        icons: [
            {
                src: '/assets/images/web-app-manifest-192x192.png',
                sizes: '192x192',
                type: 'image/png',
            },
            {
                src: '/assets/images/web-app-manifest-512x512.png',
                sizes: '512x512',
                type: 'image/png',
            },
        ],
    };
}

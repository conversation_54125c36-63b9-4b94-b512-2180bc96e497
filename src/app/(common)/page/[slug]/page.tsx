import ItemText from '@/components/common/ItemText';
import { getArticle, getArticles } from '@/services/ArticleService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import Article from '@/types/Article';
import { GlobalConfig } from '@/types/GlobalConfig';
import { API_URL } from '@/utils/constants';
import { isEmpty, last, split } from 'lodash';
import { marked } from 'marked';
import { notFound } from 'next/navigation';

export async function generateMetadata({ params }: { params: { slug: string } }) {
    const id = last(split(params.slug, '-'));
    let data: Article | null = null;
    let config: GlobalConfig | null = null;
    if (id) {
        [data, config] = await Promise.all([getArticle(id), getGlobalConfig()]);
    }
    const siteTitle = config?.siteName ?? 'Học viện yêu B & G';
    const siteImage = `${API_URL}${config?.logoFooter.url}`;
    const title = data?.title ? `${data?.title} - ${siteTitle}` : siteTitle;
    const description = data?.description ?? config?.siteDesc ?? 'Học viện Yêu Boys & Girls';
    const image = data?.image?.url;
    return {
        title,
        description,
        keywords: data?.keywords ? `${data?.keywords}, ${config?.keywords}` : (config?.keywords ?? ''),
        openGraph: {
            title,
            description,
            images: [
                {
                    url: image ? `${API_URL}${image}` : siteImage,
                    width: image ? data?.image.width : config?.logoFooter.width,
                    height: image ? data?.image.height : config?.logoFooter.height,
                    alt: title,
                },
            ],
            type: 'article',
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            images: [image ? `${API_URL}${image}` : siteImage],
        },
    };
}

export default async function PageDetail({ params }: { params: { slug: string } }) {
    let data: Article | null = null;
    const articles = await getArticles({ slug: params.slug, isPage: true });
    if (!isEmpty(articles.data)) {
        data = articles.data![0];
        data.content = data.content ? await marked.parse(data.content) : '';
    } else notFound();

    return data ? (
        <div className="rbt-overlay-page-wrapper">
            <div className="breadcrumb-image-container breadcrumb-style-max-width">
                <div className="breadcrumb-image-wrapper">
                    <img src="/assets/images/bg/bg-image-10.jpg" alt="" />
                </div>
                <div className="breadcrumb-content-top text-center">
                    <h1 className="title">{data.title}</h1>
                    <p>{data.description}</p>
                </div>
            </div>
            <div className="rbt-blog-details-area rbt-section-gapBottom breadcrumb-style-max-width">
                <div className="blog-content-wrapper rbt-article-content-wrapper">
                    <div className="content">
                        <ItemText text={data.content} className={[]} />
                    </div>
                </div>
            </div>
        </div>
    ) : (
        <></>
    );
}

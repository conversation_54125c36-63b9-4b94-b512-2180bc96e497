import CourseItem from '@/components/common/CourseItem';
import { getBookmarkCourses } from '@/services/BookmarkCourseService';
import { getCourses } from '@/services/CourseService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { AUTH_KEYS } from '@/utils/constants';
import { isEmpty, uniq } from 'lodash';
import { headers } from 'next/headers';

export default async function BookmarkPage() {
    const headersList = headers();
    const userId = +(headersList.get(AUTH_KEYS.HEADER_USER_ID) ?? '0');
    const bookmarks = await getBookmarkCourses(userId);
    const courseIds = bookmarks.map((b) => b.course_id);
    const [config, data] = await Promise.all([
        getGlobalConfig(),
        isEmpty(courseIds)
            ? { data: [], meta: { pagination: { page: 0, pageSize: 0, pageCount: 0, total: 0 } } }
            : getCourses({ ids: uniq(courseIds) }),
    ]);

    return (
        <div className="rbt-dashboard-content bg-color-white rbt-shadow-box">
            <div className="content">
                <div className="section-title">
                    <h4 className="rbt-title-style-3">Bookmark</h4>
                </div>
                <div className="row g-5">
                    {(data.data ?? []).map((item) => (
                        <div className="col-md-6 col-12" key={item.id}>
                            <CourseItem item={item} userId={userId} bookmarks={bookmarks ?? []} config={config} />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

'use client';

import DashboardItem from '@/components/common/DashboardItem';
import { getUserCourses } from '@/services/UserCourseService';
import { useAuthStore } from '@/stores/authStore';
import { useCartStore } from '@/stores/cartStore';
import UserCourse from '@/types/UserCourse';
import { filter } from 'lodash';
import { useEffect, useState } from 'react';

export default function DashboardPage() {
    const [userCourses, setUserCourses] = useState<UserCourse[]>([]);
    const user = useAuthStore((state) => state.user);
    const courseIds = useCartStore((state) => state.courseIds);

    useEffect(() => {
        const getList = async (userId: number) => {
            const response = await getUserCourses(userId);
            setUserCourses(response);
        };
        if (user) getList(user.id);
    }, [user]);

    return (
        <div className="rbt-dashboard-content bg-color-white rbt-shadow-box mb--60">
            <div className="content">
                <div className="section-title">
                    <h4 className="rbt-title-style-3">Dashboard</h4>
                </div>
                <div className="row g-5">
                    <DashboardItem
                        color="primary"
                        icon="feather-book-open"
                        count={userCourses.length}
                        text="Đã đăng ký"
                    />
                    <DashboardItem
                        color="secondary"
                        icon="feather-monitor"
                        count={filter(userCourses, (item) => !item.approved).length}
                        text="Chờ xác nhận"
                    />
                    <DashboardItem color="violet" icon="feather-award" count={courseIds.length} text="Chờ thanh toán" />
                </div>
            </div>
        </div>
    );
}

'use client';

import { useAuthStore } from '@/stores/authStore';
import { FORMAT_DATE, formatDateTime } from '@/utils/date';

export default function ProfilePage() {
    const user = useAuthStore((state) => state.user);

    return (
        <div className="rbt-dashboard-content bg-color-white rbt-shadow-box">
            <div className="content">
                <div className="section-title">
                    <h4 className="rbt-title-style-3"><PERSON>ồ sơ của tôi</h4>
                </div>
                <div className="rbt-profile-row row row--15">
                    <div className="col-lg-4 col-md-4">
                        <div className="rbt-profile-content b2">Họ và tên</div>
                    </div>
                    <div className="col-lg-8 col-md-8">
                        <div className="rbt-profile-content b2">{user?.username}</div>
                    </div>
                </div>
                <div className="rbt-profile-row row row--15 mt--15">
                    <div className="col-lg-4 col-md-4">
                        <div className="rbt-profile-content b2">Email</div>
                    </div>
                    <div className="col-lg-8 col-md-8">
                        <div className="rbt-profile-content b2">{user?.email}</div>
                    </div>
                </div>
                <div className="rbt-profile-row row row--15 mt--15">
                    <div className="col-lg-4 col-md-4">
                        <div className="rbt-profile-content b2">Ngày tạo</div>
                    </div>
                    <div className="col-lg-8 col-md-8">
                        <div className="rbt-profile-content b2">
                            {user?.createdAt && formatDateTime(user.createdAt, FORMAT_DATE.SHOW_DATE_MINUTE)}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

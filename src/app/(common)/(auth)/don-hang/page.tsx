'use client';

import { getCourses } from '@/services/CourseService';
import { getOrders } from '@/services/OrderService';
import { useAuthStore } from '@/stores/authStore';
import Course from '@/types/Course';
import Order, { OrderData, ShippingData } from '@/types/Order';
import { API_URL, COURSE_ROUTE, PRODUCT_ROUTE } from '@/utils/constants';
import { FORMAT_DATE, formatDateTime } from '@/utils/date';
import classNames from 'classnames';
import { find, isEmpty, uniq } from 'lodash';
import Link from 'next/link';
import { Fragment, useEffect, useState } from 'react';
import { getProducts } from '@/services/ProductService';
import Product from '@/types/Product';
import { formatNumber } from '@/utils';

export default function OrderListPage() {
    const [orders, setOrders] = useState<Order[]>([]);
    const [courses, setCourses] = useState<Course[]>([]);
    const [products, setProducts] = useState<Product[]>([]);
    const user = useAuthStore((state) => state.user);

    useEffect(() => {
        const getList = async (userId: number) => {
            const response = await getOrders(userId);
            setOrders(response ?? []);
            const courseIds: string[] = [];
            const productIds: string[] = [];
            response.forEach((order) => {
                (order.data as OrderData[]).forEach((item) => {
                    if (item.isCourse) courseIds.push(item.id);
                    else productIds.push(item.id);
                });
            });
            if (!isEmpty(courseIds)) {
                const result = await getCourses({ documentIds: uniq(courseIds) });
                setCourses(result.data ?? []);
            }
            if (!isEmpty(productIds)) {
                const result = await getProducts({ documentIds: uniq(productIds) });
                setProducts(result.data ?? []);
            }
        };
        if (user) getList(user.id);
    }, [user]);

    const getLinkItem = (documentId: string, isTitle: boolean, isCourse: boolean) => {
        if (isCourse) {
            const item = find(courses, { documentId });
            if (item) {
                return (
                    <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                        {isTitle ? item.title : <img src={`${API_URL}${item.image.url}`} alt={item.title} />}
                    </Link>
                );
            }
        } else {
            const item = find(products, { documentId });
            if (item) {
                return (
                    <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>
                        {isTitle ? item.title : <img src={`${API_URL}${item.image.url}`} alt={item.title} />}
                    </Link>
                );
            }
        }
        return <></>;
    };

    return (
        <div className="rbt-dashboard-content bg-color-white rbt-shadow-box">
            <div className="content">
                <div className="section-title">
                    <h4 className="rbt-title-style-3">Đơn hàng đã mua</h4>
                </div>
                <div className="rbt-dashboard-table table-responsive mobile-table-750">
                    <table className="rbt-table table table-borderless">
                        <thead>
                            <tr>
                                <th colSpan={3}>Đơn hàng</th>
                            </tr>
                        </thead>
                        <tbody>
                            {orders.map((item) => (
                                <Fragment key={item.id}>
                                    <tr className="bg-color-grey">
                                        <td style={{ width: '180px' }}>
                                            Mã: {item.code}
                                            <br />
                                            <span
                                                className={classNames('rbt-badge-5', {
                                                    'bg-color-success-opacity color-success': item.approved,
                                                    'bg-color-warning-opacity color-warning': !item.approved,
                                                })}
                                            >
                                                {item.approved ? 'Thành công' : 'Chờ xác nhận'}
                                            </span>
                                        </td>
                                        <td style={{ width: '200px' }}>
                                            Ngày: {formatDateTime(item.createdAt!, FORMAT_DATE.SHOW_ONLY_DATE)}
                                            <br />
                                            Tổng tiền:{' '}
                                            <span className="theme-gradient">{formatNumber(item.price)}đ</span>
                                        </td>
                                        <td>
                                            {!isEmpty((item.shipping as ShippingData).name) && (
                                                <>
                                                    {(item.shipping as ShippingData).name} -{' '}
                                                    {(item.shipping as ShippingData).phone} -{' '}
                                                    {(item.shipping as ShippingData).address},{' '}
                                                    {(item.shipping as ShippingData).ward},{' '}
                                                    {(item.shipping as ShippingData).district},{' '}
                                                    {(item.shipping as ShippingData).province}
                                                </>
                                            )}
                                        </td>
                                    </tr>
                                    {!isEmpty((item.shipping as ShippingData).comment) && (
                                        <tr className="rbt-border-bottom">
                                            <td colSpan={3}>Ghi chú: {(item.shipping as ShippingData).comment}</td>
                                        </tr>
                                    )}
                                    <tr>
                                        <td colSpan={3}>
                                            <ul className="rbt-minicart-wrapper">
                                                {(item.data as OrderData[]).map((item1, index) => (
                                                    <li key={index} className="minicart-item">
                                                        <div className="thumbnail">
                                                            {getLinkItem(item1.id, false, item1.isCourse)}
                                                        </div>
                                                        <div className="product-content">
                                                            <span
                                                                className={classNames('rbt-badge-5 mb--5', {
                                                                    'bg-color-primary-opacity color-primary':
                                                                        item1.isCourse,
                                                                    'bg-color-success-opacity color-success':
                                                                        !item1.isCourse,
                                                                })}
                                                            >
                                                                {item1.isCourse ? 'Khoá học' : 'Sản phẩm'}
                                                            </span>
                                                            <h6 className="title order-code">
                                                                {getLinkItem(item1.id, true, item1.isCourse)}
                                                            </h6>
                                                            <span className="quantity order-code">
                                                                {!item1.isCourse && (
                                                                    <span className="price">
                                                                        {item1.quantity} x {formatNumber(item1.price)}đ
                                                                        ={' '}
                                                                    </span>
                                                                )}
                                                                <span className="theme-gradient">
                                                                    {formatNumber(item1.price * item1.quantity)}đ
                                                                </span>
                                                            </span>
                                                        </div>
                                                    </li>
                                                ))}
                                            </ul>
                                        </td>
                                    </tr>
                                </Fragment>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}

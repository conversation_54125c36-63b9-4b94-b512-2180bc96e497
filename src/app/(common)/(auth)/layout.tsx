'use client';

import UserLinkList from '@/components/common/UserLinkList';
import { useAuthStore } from '@/stores/authStore';
import { useRouter } from 'next/navigation';
import React from 'react';
import { logoutLocal } from '@/services/UserService';

export default function AuthLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    const user = useAuthStore((state) => state.user);
    const setUser = useAuthStore((state) => state.setUser);
    const router = useRouter();

    const logout = async () => {
        if (user) {
            await logoutLocal(user.id, user.jwt!);
        }
        setUser(null);
        router.push('/');
    };

    return (
        <>
            <div className="rbt-breadcrumb-default ptb--100 ptb_md--50 ptb_sm--30 bg-gradient-1">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="breadcrumb-inner text-center">
                                <h2 className="title"></h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="rbt-dashboard-area rbt-section-overlayping-top rbt-section-gapBottom mt_sm--0">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="row g-5">
                                <div className="col-lg-3">
                                    <div className="rbt-default-sidebar sticky-top rbt-shadow-box rbt-gradient-border">
                                        <div className="inner">
                                            <div className="content-item-content">
                                                <div className="rbt-default-sidebar-wrapper">
                                                    <div className="section-title mb--20">
                                                        <h6 className="rbt-title-style-2">Xin chào {user?.username}</h6>
                                                    </div>
                                                    <nav className="mainmenu-nav">
                                                        <ul className="dashboard-mainmenu rbt-default-sidebar-list">
                                                            <UserLinkList />
                                                        </ul>
                                                    </nav>
                                                    <nav className="mainmenu-nav mt--40">
                                                        <ul className="dashboard-mainmenu rbt-default-sidebar-list">
                                                            <li>
                                                                <a className="cursor-pointer" onClick={logout}>
                                                                    <i className="feather-log-out" />
                                                                    <span>Đăng xuất</span>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </nav>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-lg-9">{children}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

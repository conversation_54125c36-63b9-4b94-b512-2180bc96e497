import Breadcrumb from '@/components/common/Breadcrumb';
import { getGlobalConfig } from '@/services/SingleTypeService';

export async function generateMetadata() {
    const config = await getGlobalConfig();
    const title = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: `Gói đăng ký - ${title}`,
    };
}

export default async function SubscriptionPage() {
    return (
        <div>
            <div className="rbt-breadcrumb-default ptb--100 ptb_md--50 ptb_sm--30 bg-gradient-1">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="breadcrumb-inner text-center">
                                <h2 className="title">Gói đăng ký</h2>
                                <Breadcrumb
                                    items={[
                                        { id: 1, name: 'Trang chủ', link: '/' },
                                        { id: 0, name: '<PERSON><PERSON><PERSON> đăng ký', link: '' },
                                    ]}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="rbt-pricing-area bg-color-white rbt-section-gap">
                <div className="container">
                    <div className="row g-5 mb--60">
                        <div className="col-12">
                            <div className="pricing-billing-duration text-center">
                                <ul>
                                    <li className="nav-item">
                                        <button className="nav-link yearly-plan-btn" type="button">
                                            Yearly Plan
                                        </button>
                                    </li>
                                    <li className="nav-item">
                                        <button className="nav-link monthly-plan-btn active" type="button">
                                            Monthly Plan
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div className="row g-5">
                        {/* Start Single Pricing  */}
                        <div className="col-xl-4 col-lg-6 col-md-6 col-12">
                            <div className="pricing-table style-2">
                                <div className="pricing-header">
                                    <h3 className="title color-primary">Basic Plan</h3>
                                    <span className="rbt-badge mb--35">Free for a Month</span>
                                    <div className="price-wrap">
                                        <div className="yearly-pricing" style={{ display: 'none' }}>
                                            <span className="amount color-primary">$30.99</span>
                                            <span className="duration color-primary">/yearly</span>
                                        </div>
                                        <div className="monthly-pricing" style={{ display: 'block' }}>
                                            <span className="amount color-primary">$10.00</span>
                                            <span className="duration color-primary">/monthly</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="pricing-btn">
                                    <a className="rbt-btn bg-primary-opacity hover-icon-reverse w-100" href="#">
                                        <div className="icon-reverse-wrapper">
                                            <span className="btn-text">Join Course Plan</span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                        </div>
                                    </a>
                                </div>
                                <div className="pricing-body">
                                    <ul className="list-item">
                                        <li>
                                            <i className="feather-check" /> Unlimited Access Courses
                                        </li>
                                        <li>
                                            <i className="feather-check" /> Certificate After Completion
                                        </li>
                                        <li className="off">
                                            <i className="feather-x" /> 24/7 Dedicated Support
                                        </li>
                                        <li className="off">
                                            <i className="feather-x" /> Unlimited Emails
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {/* End Single Pricing  */}
                        {/* Start Single Pricing  */}
                        <div className="col-xl-4 col-lg-6 col-md-6 col-12">
                            <div className="pricing-table style-2 active">
                                <div className="pricing-header">
                                    <div className="pricing-badge">
                                        <span>Popular</span>
                                    </div>
                                    <h3 className="title color-secondary">Standard Plan</h3>
                                    <span className="rbt-badge mb--35">Most Popular</span>
                                    <div className="price-wrap">
                                        <div className="yearly-pricing" style={{ display: 'none' }}>
                                            <span className="amount color-secondary">$100.99</span>
                                            <span className="duration color-secondary">/yearly</span>
                                        </div>
                                        <div className="monthly-pricing" style={{ display: 'block' }}>
                                            <span className="amount color-secondary">$20.00</span>
                                            <span className="duration color-secondary">/monthly</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="pricing-btn">
                                    <a className="rbt-btn bg-secondary-opacity hover-icon-reverse w-100" href="#">
                                        <div className="icon-reverse-wrapper">
                                            <span className="btn-text">Join Course Plan</span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                        </div>
                                    </a>
                                </div>
                                <div className="pricing-body">
                                    <ul className="list-item">
                                        <li>
                                            <i className="feather-check" /> Unlimited Access Courses
                                        </li>
                                        <li>
                                            <i className="feather-check" /> Certificate After Completion
                                        </li>
                                        <li>
                                            <i className="feather-check" /> High Resolution Videos
                                        </li>
                                        <li>
                                            <i className="feather-check" /> 24/7 Dedicated Support
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {/* End Single Pricing  */}
                        {/* Start Single Pricing  */}
                        <div className="col-xl-4 col-lg-6 col-md-6 col-12">
                            <div className="pricing-table style-2">
                                <div className="pricing-header">
                                    <h3 className="title color-pink">Exclusive Plan</h3>
                                    <span className="rbt-badge mb--35">Free for a Month</span>
                                    <div className="price-wrap">
                                        <div className="yearly-pricing" style={{ display: 'none' }}>
                                            <span className="amount color-pink">$99.99</span>
                                            <span className="duration color-pink">/yearly</span>
                                        </div>
                                        <div className="monthly-pricing" style={{ display: 'block' }}>
                                            <span className="amount color-pink">$39.00</span>
                                            <span className="duration color-pink">/monthly</span>
                                        </div>
                                    </div>
                                </div>
                                <div className="pricing-btn">
                                    <a className="rbt-btn bg-pink-opacity hover-icon-reverse w-100" href="#">
                                        <div className="icon-reverse-wrapper">
                                            <span className="btn-text">Join Course Plan</span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                        </div>
                                    </a>
                                </div>
                                <div className="pricing-body">
                                    <ul className="list-item">
                                        <li>
                                            <i className="feather-check" /> Unlimited Access Courses
                                        </li>
                                        <li>
                                            <i className="feather-check" /> Certificate After Completion
                                        </li>
                                        <li className="off">
                                            <i className="feather-x" /> 24/7 Dedicated Support
                                        </li>
                                        <li className="off">
                                            <i className="feather-x" /> Unlimited Emails
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        {/* End Single Pricing  */}
                    </div>
                </div>
            </div>
            <div className="rbt-accordion-area accordion-style-1 bg-color-extra2 rbt-section-gap">
                <div className="container">
                    <div className="row g-5 align-items-center">
                        <div className="col-lg-10 offset-lg-1">
                            <div className="rbt-accordion-style accordion">
                                <div className="section-title text-center mb--60">
                                    <span className="subtitle bg-pink-opacity">Frequently Asked Questions</span>
                                    <h2 className="title">
                                        Have a Question with <br /> Histudy University?
                                    </h2>
                                    <p className="description has-medium-font-size mt--20">
                                        <strong>Its an educational platform</strong> Lorem Ipsum is simply dummy text of
                                        the printing and typesetting industry.
                                    </p>
                                </div>
                                <div className="rbt-accordion-style rbt-accordion-04 accordion">
                                    <div className="accordion" id="accordionExamplec3">
                                        <div className="accordion-item card">
                                            <h2 className="accordion-header card-header" id="headingThree1">
                                                <button
                                                    className="accordion-button"
                                                    type="button"
                                                    data-bs-toggle="collapse"
                                                    data-bs-target="#collapseThree1"
                                                    aria-expanded="true"
                                                    aria-controls="collapseThree1"
                                                >
                                                    What is Histudy ? How does it work?
                                                </button>
                                            </h2>
                                            <div
                                                id="collapseThree1"
                                                className="accordion-collapse collapse show"
                                                aria-labelledby="headingThree1"
                                                data-bs-parent="#accordionExamplec3"
                                            >
                                                <div className="accordion-body card-body">
                                                    You can run Histudy easily. Any School, University, College can be
                                                    use this histudy education template for their educational purpose. A
                                                    university can be run their online leaning management system by
                                                    histudy education template.
                                                </div>
                                            </div>
                                        </div>
                                        <div className="accordion-item card">
                                            <h2 className="accordion-header card-header" id="headingThree2">
                                                <button
                                                    className="accordion-button collapsed"
                                                    type="button"
                                                    data-bs-toggle="collapse"
                                                    data-bs-target="#collapseThree2"
                                                    aria-expanded="false"
                                                    aria-controls="collapseThree2"
                                                >
                                                    How can I get the customer support?
                                                </button>
                                            </h2>
                                            <div
                                                id="collapseThree2"
                                                className="accordion-collapse collapse"
                                                aria-labelledby="headingThree2"
                                                data-bs-parent="#accordionExamplec3"
                                            >
                                                <div className="accordion-body card-body">
                                                    After purchasing the product need you any support you can be share
                                                    with us with sending <NAME_EMAIL>.
                                                </div>
                                            </div>
                                        </div>
                                        <div className="accordion-item card">
                                            <h2 className="accordion-header card-header" id="headingThree3">
                                                <button
                                                    className="accordion-button collapsed"
                                                    type="button"
                                                    data-bs-toggle="collapse"
                                                    data-bs-target="#collapseThree3"
                                                    aria-expanded="false"
                                                    aria-controls="collapseThree3"
                                                >
                                                    Can I get update regularly and For how long do I get updates?
                                                </button>
                                            </h2>
                                            <div
                                                id="collapseThree3"
                                                className="accordion-collapse collapse"
                                                aria-labelledby="headingThree3"
                                                data-bs-parent="#accordionExamplec3"
                                            >
                                                <div className="accordion-body card-body">
                                                    Yes, We will get update the Histudy. And you can get it any time.
                                                    Next time we will comes with more feature. You can be get update for
                                                    unlimited times. Our dedicated team works for update.
                                                </div>
                                            </div>
                                        </div>
                                        <div className="accordion-item card">
                                            <h2 className="accordion-header card-header" id="headingThree4">
                                                <button
                                                    className="accordion-button collapsed"
                                                    type="button"
                                                    data-bs-toggle="collapse"
                                                    data-bs-target="#collapseThree4"
                                                    aria-expanded="false"
                                                    aria-controls="collapseThree4"
                                                >
                                                    15 Things To Know About Education?
                                                </button>
                                            </h2>
                                            <div
                                                id="collapseThree4"
                                                className="accordion-collapse collapse"
                                                aria-labelledby="headingThree4"
                                                data-bs-parent="#accordionExamplec3"
                                            >
                                                <div className="accordion-body card-body">
                                                    If you're looking for random paragraphs, you've come to the right
                                                    place. When a random word or a random sentence isn't quite enough,
                                                    the next logical step is to find a random paragraph.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

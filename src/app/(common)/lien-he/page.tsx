import ContactForm from '@/components/common/ContactForm';
import { getGlobalConfig } from '@/services/SingleTypeService';

export async function generateMetadata() {
    const config = await getGlobalConfig();
    const title = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: `Liên hệ - ${title}`,
    };
}

export default async function ContactPage() {
    const config = await getGlobalConfig();

    return (
        <>
            <div className="rbt-conatct-area bg-gradient-11 rbt-section-gap">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="section-title text-center mb--60">
                                <span className="subtitle bg-secondary-opacity">Liên hệ</span>
                                <h2 className="title">Học viện Yêu Boys & Girls</h2>
                            </div>
                        </div>
                    </div>
                    <div className="row g-5">
                        <div className="col-lg-4 col-md-6 col-sm-6 col-12 sal-animate">
                            <div className="rbt-address">
                                <div className="icon">
                                    <i className="feather-headphones" />
                                </div>
                                <div className="inner">
                                    <h4 className="title">Điện thoại</h4>
                                    <p>
                                        <a href={`tel:${config?.phone}`}>{config?.phone}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6 col-sm-6 col-12 sal-animate">
                            <div className="rbt-address">
                                <div className="icon">
                                    <i className="feather-mail" />
                                </div>
                                <div className="inner">
                                    <h4 className="title">Email</h4>
                                    <p>
                                        <a href={`mailto:${config?.email}`}>{config?.email}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div className="col-lg-4 col-md-6 col-sm-6 col-12 sal-animate">
                            <div className="rbt-address">
                                <div className="icon">
                                    <i className="feather-map-pin" />
                                </div>
                                <div className="inner">
                                    <h4 className="title">Địa chỉ</h4>
                                    <p>{config?.address}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ContactForm />
            <div className="rbt-google-map bg-color-white rbt-section-gapTop">
                <iframe
                    className="w-100"
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d36233.834922628725!2d105.81636412139991!3d21.022738359983414!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e1!3m2!1sen!2s!4v1727615350829!5m2!1sen!2s"
                    height={600}
                    style={{ border: 0 }}
                />
            </div>
        </>
    );
}

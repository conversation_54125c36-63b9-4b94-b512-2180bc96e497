import { getCategories } from '@/services/CategoryService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { getSearchCourseRoute } from '@/utils';
import Link from 'next/link';

export async function generateMetadata() {
    const config = await getGlobalConfig();
    const title = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: `Thể loại khoá học - ${title}`,
    };
}

export default async function CourseCategoryList() {
    const categories = await getCategories();

    return (
        <main className="rbt-main-wrapper">
            <div className="rbt-categories-area bg-color-white rbt-section-gap">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="section-title text-center">
                                <span className="subtitle bg-primary-opacity">Thể loạ<PERSON></span>
                                <h2 className="title">
                                    H<PERSON><PERSON> trải nghiệm và thực hành để có một cuộc yêu tuyệt vời.
                                    <br />
                                    Khám phá hơn 100 khóa học online trực tuyến tại B & G.
                                </h2>
                            </div>
                        </div>
                    </div>
                    <div className="row g-5 mt--20">
                        {(categories ?? []).map((item) => (
                            <div key={item.id} className="col-lg-3 col-md-6 col-sm-6 col-12">
                                <Link
                                    className="rbt-cat-box rbt-cat-box-1 text-center"
                                    href={getSearchCourseRoute({ categoryId: item.id, slug: item.slug })}
                                >
                                    <div className="inner">
                                        <div className="icons">
                                            <img
                                                src={`/assets/images/category/${(item.id % 8) + 1}.png`}
                                                alt={item.title}
                                            />
                                        </div>
                                        <div className="content">
                                            <h5 className="title">{item.title}</h5>
                                            <div className="read-more-btn">
                                                <span className="rbt-btn-link">
                                                    25 Courses
                                                    <i className="feather-arrow-right" />
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </main>
    );
}

import HomeAbout from '@/components/common/HomeAbout';
import Newsletter from '@/components/common/Newsletter';
import HomeBlog from '@/components/home/<USER>';
import HomeCourses from '@/components/home/<USER>';
import HomeEvents from '@/components/home/<USER>';
import HomeSlider from '@/components/home/<USER>';
import { getArticles } from '@/services/ArticleService';
import { getBookmarkCourses } from '@/services/BookmarkCourseService';
import { getCourses } from '@/services/CourseService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { AUTH_KEYS } from '@/utils/constants';
import { headers } from 'next/headers';

export default async function Home() {
    const headersList = headers();
    const userId = +(headersList.get(AUTH_KEYS.HEADER_USER_ID) ?? '0');
    const [config, highlightCourses, lastedNews, bookmarks] = await Promise.all([
        getGlobalConfig(),
        getCourses({ highlight: true }),
        getArticles({ limit: 4, isPage: false }),
        getBookmarkCourses(userId),
    ]);

    return (
        <>
            <HomeSlider config={config} />
            <HomeCourses
                items={(highlightCourses.data ?? []).filter((item) => !item.is_offline).slice(0, 12)}
                userId={userId}
                bookmarks={bookmarks ?? []}
                config={config}
            />
            <HomeAbout showButton={true} />
            <HomeEvents items={(highlightCourses.data ?? []).filter((item) => item.is_offline)} />
            <HomeBlog items={lastedNews.data ?? []} />
            <Newsletter config={config} />
        </>
    );
}

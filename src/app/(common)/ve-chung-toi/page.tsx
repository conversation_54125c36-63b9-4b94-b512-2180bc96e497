import HomeAbout from '@/components/common/HomeAbout';
import Newsletter from '@/components/common/Newsletter';
import { getGlobalConfig } from '@/services/SingleTypeService';

export async function generateMetadata() {
    const config = await getGlobalConfig();
    const title = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: `Về chúng tôi - ${title}`,
    };
}

export default async function AboutPage() {
    const config = await getGlobalConfig();

    return (
        <>
            <div className="slider-area rbt-banner-10 height-750 bg_image bg_image--11" data-black-overlay={5}>
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="inner text-center">
                                <div className="section-title mb--20">
                                    <span className="subtitle bg-coral-opacity">Về chúng tôi</span>
                                </div>
                                <h1 className="title display-one text-white">
                                    Take Challenge for Build Your Life. <br />
                                    The World Most Lessons for Back to Your Life.
                                </h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <HomeAbout />
            <div className="rbt-service-area bg-color-white rbt-section-gapBottom">
                <div className="container">
                    <div className="row mb--60">
                        <div className="col-lg-12">
                            <div className="section-title text-center">
                                <h2 className="title">Why learn with our courses?</h2>
                                <p className="description mt--30">
                                    <strong>Histudy educational platform</strong> ipsum dolor sit amet consectetur
                                    adipisicing elit.
                                </p>
                            </div>
                        </div>
                    </div>
                    {/* Start Card Area */}
                    <div className="row row--15 mt_dec--30">
                        {/* Start Service Grid  */}
                        <div className="col-lg-4 col-xl-3 col-xxl-3 col-md-6 col-sm-6 col-12 mt--30">
                            <div className="service-card service-card-6">
                                <div className="inner">
                                    <div className="icon">
                                        <img src="assets/images/icons/001-bulb.png" alt="icons Images" />
                                    </div>
                                    <div className="content">
                                        <h6 className="title">
                                            <a href="#">Your Apply</a>
                                        </h6>
                                        <p className="description">
                                            English Learning looking for random paragraphs, you've come to the right
                                            place.
                                        </p>
                                    </div>
                                    <span className="number-text">1</span>
                                </div>
                            </div>
                        </div>
                        {/* End Service Grid  */}
                        {/* Start Service Grid  */}
                        <div className="col-lg-4 col-xl-3 col-xxl-3 col-md-6 col-sm-6 col-12 mt--30">
                            <div className="service-card service-card-6">
                                <div className="inner">
                                    <div className="icon">
                                        <img src="assets/images/icons/002-hat.png" alt="Shape Images" />
                                    </div>
                                    <div className="content">
                                        <h6 className="title">
                                            <a href="#">We Connect</a>
                                        </h6>
                                        <p className="description">
                                            Javascript Learning looking for random paragraphs, you've come to the right
                                            place.
                                        </p>
                                    </div>
                                    <span className="number-text">2</span>
                                </div>
                            </div>
                        </div>
                        {/* End Service Grid  */}
                        {/* Start Service Grid  */}
                        <div className="col-lg-4 col-xl-3 col-xxl-3 col-md-6 col-sm-6 col-12 mt--30">
                            <div className="service-card service-card-6">
                                <div className="inner">
                                    <div className="icon">
                                        <img src="assets/images/icons/003-id-card.png" alt="Shape Images" />
                                    </div>
                                    <div className="content">
                                        <h6 className="title">
                                            <a href="#">You Get Ready</a>
                                        </h6>
                                        <p className="description">
                                            Angular Learning looking for random paragraphs, you've come to the right
                                            place.
                                        </p>
                                    </div>
                                    <span className="number-text">3</span>
                                </div>
                            </div>
                        </div>
                        {/* End Service Grid  */}
                        {/* Start Service Grid  */}
                        <div className="col-lg-4 col-xl-3 col-xxl-3 col-md-6 col-sm-6 col-12 mt--30">
                            <div className="service-card service-card-6">
                                <div className="inner">
                                    <div className="icon">
                                        <img src="assets/images/icons/004-pass.png" alt="Shape Images" />
                                    </div>
                                    <div className="content">
                                        <h6 className="title">
                                            <a href="#">Completed</a>
                                        </h6>
                                        <p className="description">
                                            Php Learning looking for random paragraphs, you've come to the right place.
                                        </p>
                                    </div>
                                    <span className="number-text">4</span>
                                </div>
                            </div>
                        </div>
                        {/* End Service Grid  */}
                    </div>
                    {/* End Card Area */}
                </div>
            </div>
            <Newsletter config={config} />
        </>
    );
}

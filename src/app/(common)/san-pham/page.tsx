import Breadcrumb from '@/components/common/Breadcrumb';
import PageTitle from '@/components/common/PageTitle';
import Pagination from '@/components/common/Pagination';
import { getProducts } from '@/services/ProductService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { SearchProduct } from '@/types/Product';
import { COURSE_LIMIT, PRODUCT_ROUTE } from '@/utils/constants';
import { find, get, isEmpty } from 'lodash';
import { getCategories } from '@/services/CategoryService';
import ProductSearchForm from '@/components/product/ProductSearchForm';
import ProductItem from '@/components/product/ProductItem';

const getPageTitle = (categoryTitle?: string, q?: string) => {
    if (q) return `Tìm kiếm sản phẩm "${q}"`;
    if (categoryTitle) return categoryTitle;
    return 'Tất cả sản phẩm';
};

export async function generateMetadata({
    searchParams,
}: {
    searchParams?: Record<string, string | string[] | undefined>;
}) {
    const categoryId = +get(searchParams, 'cId', 0);
    const [config, categories] = await Promise.all([
        getGlobalConfig(),
        categoryId > 0 ? getCategories(false, categoryId) : null,
    ]);
    const siteTitle = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: `${getPageTitle(categories?.[0]?.title, get(searchParams, 'q')?.toString())} - ${siteTitle}`,
        keywords: !isEmpty(categories) ? `${categories![0].title}, ${config?.keywords}` : (config?.keywords ?? ''),
    };
}

export default async function ProductList({
    searchParams,
}: Readonly<{ searchParams?: Record<string, string | string[] | undefined> }>) {
    const page = +get(searchParams, 'page', 1);
    const searchProduct: SearchProduct = {
        page: page <= 0 ? 1 : page,
        limit: COURSE_LIMIT,
        q: get(searchParams, 'q')?.toString(),
        sortBy: +get(searchParams, 'sortBy', 0),
        categoryId: +get(searchParams, 'cId', 0),
        priceRange: +get(searchParams, 'priceRange', -1),
    };
    const [categories, data] = await Promise.all([getCategories(false), getProducts(searchProduct)]);
    const category = searchProduct.categoryId! > 0 ? find(categories, { id: searchProduct.categoryId }) : undefined;
    searchProduct.slug = category?.slug;

    return (
        <>
            <div className="rbt-page-banner-wrapper">
                <div className="rbt-banner-image" />
                <div className="rbt-banner-content">
                    <div className="rbt-banner-content-top">
                        <div className="container">
                            <div className="row">
                                <div className="col-lg-12">
                                    <Breadcrumb
                                        items={
                                            category
                                                ? [
                                                      { id: 1, name: 'Trang chủ', link: '/' },
                                                      { id: 0, name: category.title, link: '' },
                                                  ]
                                                : [
                                                      { id: 1, name: 'Trang chủ', link: '/' },
                                                      {
                                                          id: 0,
                                                          name: getPageTitle(undefined, searchProduct.q),
                                                          link: '',
                                                      },
                                                  ]
                                        }
                                    />
                                    <PageTitle
                                        title={getPageTitle(category?.title, searchProduct.q)}
                                        description="Sản phẩm tại B & G."
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="rbt-section-overlayping-top rbt-section-gapBottom">
                <div className="container">
                    <div className="row row--30 gy-5">
                        <div className="col-lg-3 order-2 order-lg-1">
                            <ProductSearchForm categories={categories ?? []} searchParams={searchProduct} />
                        </div>
                        <div className="col-lg-9 order-1 order-lg-2">
                            <div className="rbt-course-grid-column">
                                {(data.data ?? []).map((item) => (
                                    <div className="course-grid-2" key={item.id}>
                                        <ProductItem item={item} />
                                    </div>
                                ))}
                            </div>
                            {data.meta && (
                                <Pagination
                                    baseUrl={PRODUCT_ROUTE}
                                    searchParams={searchProduct}
                                    pagination={data.meta.pagination}
                                />
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

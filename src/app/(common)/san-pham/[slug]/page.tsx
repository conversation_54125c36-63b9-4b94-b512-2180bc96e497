import { getProduct, getProducts } from '@/services/ProductService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { ResponseList } from '@/types/common';
import Product from '@/types/Product';
import { GlobalConfig } from '@/types/GlobalConfig';
import { API_URL, COURSE_LIMIT, PRODUCT_ROUTE } from '@/utils/constants';
import { isEmpty, last, split } from 'lodash';
import { notFound } from 'next/navigation';
import Breadcrumb from '@/components/common/Breadcrumb';
import CoursePrice from '@/components/common/CoursePrice';
import ProductItem from '@/components/product/ProductItem';
import { marked } from 'marked';
import ProductTab from '@/components/product/ProductTab';
import Link from 'next/link';
import AddCartMainProduct from '@/components/product/AddCartMainProduct';

export async function generateMetadata({ params }: { params: { slug: string } }) {
    const id = last(split(params.slug, '-'));
    let data: Product | null = null;
    let config: GlobalConfig | null = null;
    if (id) {
        [data, config] = await Promise.all([getProduct(id), getGlobalConfig()]);
    }
    const siteTitle = config?.siteName ?? 'Học viện yêu B & G';
    const siteImage = `${API_URL}${config?.logoFooter.url}`;
    const title = data?.title ? `${data?.title} - ${siteTitle}` : siteTitle;
    const description = data?.description ?? config?.siteDesc ?? 'Học viện Yêu Boys & Girls';
    const image = data?.image?.url;
    return {
        title,
        description,
        keywords: data?.keywords ? `${data?.keywords}, ${config?.keywords}` : (config?.keywords ?? ''),
        openGraph: {
            title,
            description,
            images: [
                {
                    url: image ? `${API_URL}${image}` : siteImage,
                    width: image ? data?.image.width : config?.logoFooter.width,
                    height: image ? data?.image.height : config?.logoFooter.height,
                    alt: title,
                },
            ],
            type: 'article',
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            images: [image ? `${API_URL}${image}` : siteImage],
        },
    };
}

export default async function ProductDetail({ params }: { params: { slug: string } }) {
    let data: Product | null = null;
    let relatedProducts: ResponseList<Product> = { data: [] };
    const id = last(split(params.slug, '-'));
    if (id) {
        data = await getProduct(id);
        if (data) {
            data.content = data.content ? await marked.parse(data.content) : '';
            const tagIds = data.tags.map((tag) => tag.id);
            if (!isEmpty(tagIds)) {
                relatedProducts = await getProducts({
                    limit: COURSE_LIMIT,
                    differentId: data.id,
                    tagIds,
                });
            }
        } else notFound();
    } else notFound();

    return data ? (
        <>
            <div className="rbt-single-product-area rbt-single-product rbt-section-gap">
                <div className="container">
                    <div className="row g-5 row--30 align-items-center">
                        <div className="col-lg-6">
                            <div className="thumbnail">
                                <img className="w-100 radius-10" src={`${API_URL}${data.image.url}`} alt={data.title} />
                            </div>
                        </div>
                        <div className="col-lg-6">
                            <div className="content">
                                <Breadcrumb
                                    items={[
                                        { id: 1, name: 'Trang chủ', link: '/' },
                                        { id: 2, name: 'Sản phẩm', link: PRODUCT_ROUTE },
                                        { id: 0, name: data.title, link: '' },
                                    ]}
                                />
                                <h2 className="title mt--10 mb--10">{data.title}</h2>
                                {/*<span className="rbt-label-style description">By: Hal Elrod</span>*/}
                                {/*<ProductRating />*/}
                                <CoursePrice
                                    price={data.price}
                                    salePrice={data.sale_price}
                                    wrapperClass="justify-content-start mt--10"
                                />
                                <div className="mt--20 mb--20">{data.description}</div>
                                <AddCartMainProduct productId={data.documentId} />
                                <ul className="product-feature">
                                    {/*<li>*/}
                                    {/*    <span>SKU:</span> book-8*/}
                                    {/*</li>*/}
                                    <li>
                                        <span>Chuyên mục: </span>
                                        {(data.categories ?? []).map((item) => (
                                            <Link href={`/chuyen-muc/${item.slug}-${item.id}`} key={item.id}>
                                                {item.title}
                                            </Link>
                                        ))}
                                    </li>
                                    <li>
                                        <span>Tag: </span>
                                        {(data.tags ?? []).map((item) => (
                                            <Link href={`/tag/${item.slug}-${item.id}`} key={item.id}>
                                                {item.title}
                                            </Link>
                                        ))}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ProductTab content={data.content} />
            {!isEmpty(relatedProducts.data) && (
                <div className="rbt-related-product rbt-section-gapBottom bg-color-white">
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-12">
                                <div className="section-title text-center mb--50">
                                    <span className="subtitle bg-secondary-opacity">Sản phẩm liên quan</span>
                                    <h2 className="title">Sản phẩm liên quan</h2>
                                </div>
                            </div>
                        </div>
                        <div className="row g-5">
                            {relatedProducts.data!.map((item) => (
                                <div className="col-lg-4 col-md-6 col-12" key={item.id}>
                                    <ProductItem item={item} />
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </>
    ) : (
        <></>
    );
}

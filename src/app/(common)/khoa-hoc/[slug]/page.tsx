import Breadcrumb from '@/components/common/Breadcrumb';
import CourseAuthor from '@/components/common/CourseAuthor';
import CourseItem from '@/components/common/CourseItem';
import CoursePrice from '@/components/common/CoursePrice';
import PageTitle from '@/components/common/PageTitle';
import Separator from '@/components/common/Separator';
import SocialLinks from '@/components/common/SocialLinks';
import AddCartCourse from '@/components/course/AddCartCourse';
import CourseChecklist from '@/components/course/CourseChecklist';
import CourseLessons from '@/components/course/CourseLessons';
import CoursePriceBottom from '@/components/course/CoursePriceBottom';
import CourseRating from '@/components/course/CourseRating';
import CourseReview from '@/components/course/CourseReview';
import VideoPreview from '@/components/course/VideoPreview';
import { getBookmarkCourses } from '@/services/BookmarkCourseService';
import { getCourse, getCourses } from '@/services/CourseService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { getUserCourses } from '@/services/UserCourseService';
import BookmarkCourse from '@/types/BookmarkCourse';
import { ResponseList } from '@/types/common';
import Course from '@/types/Course';
import { GlobalConfig } from '@/types/GlobalConfig';
import UserCourse from '@/types/UserCourse';
import { API_URL, AUTH_KEYS, COURSE_LIMIT, COURSE_ROUTE } from '@/utils/constants';
import { FORMAT_DATE, formatDateTime } from '@/utils/date';
import { isEmpty, last, split } from 'lodash';
import { headers } from 'next/headers';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import CourseProducts from '@/components/course/CourseProducts';

export async function generateMetadata({ params }: { params: { slug: string } }) {
    const id = last(split(params.slug, '-'));
    let data: Course | null = null;
    let config: GlobalConfig | null = null;
    if (id) {
        [data, config] = await Promise.all([getCourse(id), getGlobalConfig()]);
    }
    const siteTitle = config?.siteName ?? 'Học viện yêu B & G';
    const siteImage = `${API_URL}${config?.logoFooter.url}`;
    const title = data?.title ? `${data?.title} - ${siteTitle}` : siteTitle;
    const description = data?.description ?? config?.siteDesc ?? 'Học viện Yêu Boys & Girls';
    const image = data?.image?.url;
    return {
        title,
        description,
        keywords: data?.keywords ? `${data?.keywords}, ${config?.keywords}` : (config?.keywords ?? ''),
        openGraph: {
            title,
            description,
            images: [
                {
                    url: image ? `${API_URL}${image}` : siteImage,
                    width: image ? data?.image.width : config?.logoFooter.width,
                    height: image ? data?.image.height : config?.logoFooter.height,
                    alt: title,
                },
            ],
            type: 'article',
        },
        twitter: {
            card: 'summary_large_image',
            title,
            description,
            images: [image ? `${API_URL}${image}` : siteImage],
        },
    };
}

export default async function CourseDetail({ params }: { params: { slug: string } }) {
    let config: GlobalConfig | null = null;
    let data: Course | null = null;
    let relatedCourses: ResponseList<Course> = { data: [] };
    let bookmarks: BookmarkCourse[] = [];
    let userCourses: UserCourse[] = [];
    const headersList = headers();
    const userId = +(headersList.get(AUTH_KEYS.HEADER_USER_ID) ?? '0');
    const id = last(split(params.slug, '-'));
    if (id) {
        [data, config, bookmarks] = await Promise.all([getCourse(id), getGlobalConfig(), getBookmarkCourses(userId)]);
        if (data) {
            userCourses = await getUserCourses(userId, data.id);
            const tagIds = data.tags.map((tag) => tag.id);
            if (!isEmpty(tagIds)) {
                relatedCourses = await getCourses({
                    limit: COURSE_LIMIT,
                    differentId: data.id,
                    tagIds,
                });
            }
        } else notFound();
    } else notFound();

    return data ? (
        <>
            <div className="rbt-breadcrumb-default rbt-breadcrumb-style-3">
                <div className="breadcrumb-inner">
                    <img src="/assets/images/bg/bg-image-10.jpg" alt="" />
                </div>
                <div className="container">
                    <div className="row">
                        <div className="col-lg-8">
                            <div className="content text-start">
                                <Breadcrumb
                                    items={[
                                        { id: 1, name: 'Trang chủ', link: '/' },
                                        { id: 2, name: 'Khoá học', link: COURSE_ROUTE },
                                        { id: 0, name: data.title, link: '' },
                                    ]}
                                />
                                <PageTitle title={data.title} description={data.description} />
                                <CourseRating tag={data.tag.title} />
                                <CourseAuthor config={config} />
                                <ul className="rbt-meta">
                                    <li>
                                        <i className="feather-calendar" />
                                        Cập nhật {formatDateTime(data.publishedAt, FORMAT_DATE.SHOW_MONTH_YEAR)}
                                    </li>
                                    <li>
                                        <i className="feather-globe" />
                                        Việt Nam
                                    </li>
                                    <li>
                                        <i className="feather-award" />
                                        Đã kiểm định
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="rbt-course-details-area ptb--60">
                <div className="container">
                    <div className="row g-5">
                        <div className="col-lg-8">
                            <div className="course-details-content">
                                <div className="rbt-course-feature-box rbt-shadow-box thuumbnail">
                                    <img className="w-100" src={`${API_URL}${data.image.url}`} alt={data.title} />
                                </div>
                                <div className="rbt-inner-onepage-navigation sticky-top mt--30">
                                    <nav className="mainmenu-nav onepagenav">
                                        <ul className="mainmenu">
                                            <li>
                                                <a href="#overview">Tổng quan</a>
                                            </li>
                                            <li>
                                                <a href="#content">Nội dung</a>
                                            </li>
                                            {!isEmpty(data.products) && (
                                                <li>
                                                    <a href="#product">Sản phẩm</a>
                                                </li>
                                            )}
                                            <li>
                                                <a href="#review">Đánh giá</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                                {/* <CourseOverview content={data.content} /> */}
                                <CourseChecklist items={data.learns} tags={data.tags} />
                                <CourseLessons
                                    chapters={data.chapters}
                                    approved={!isEmpty(userCourses) && userCourses[0].approved}
                                    slug={`${data.slug}-${data.documentId}`}
                                />
                                {!isEmpty(data.products) && (
                                    <CourseProducts productIds={(data.products ?? []).map((item) => item.documentId)} />
                                )}
                                <CourseReview />
                            </div>
                        </div>
                        <div className="col-lg-4">
                            <div className="course-sidebar sticky-top rbt-shadow-box course-sidebar-top rbt-gradient-border">
                                <div className="inner">
                                    <VideoPreview image={`${API_URL}${data.image.url}`} video={data.video} />
                                    <div className="content-item-content">
                                        <div className="rbt-price-wrapper d-flex flex-wrap align-items-center justify-content-between">
                                            <CoursePrice price={data.price} salePrice={data.sale_price} />
                                            {/* <div className="discount-time">
                                                <span className="rbt-badge color-danger bg-color-danger-opacity">
                                                    <i className="feather-clock" /> 3 days left!
                                                </span>
                                            </div> */}
                                        </div>
                                        {!isEmpty(userCourses) ? (
                                            <div className="add-to-card-button mt--15">
                                                {userCourses[0].approved ? (
                                                    <Link
                                                        className="rbt-btn btn-gradient icon-hover w-100 d-block text-center"
                                                        href={`/hoc-bai/${data.slug}-${data.documentId}/${data.chapters[0].id}/${data.chapters[0].Lessions?.[0].id}`}
                                                    >
                                                        <span className="btn-text">Vào học</span>
                                                        <span className="btn-icon">
                                                            <i className="feather-arrow-right" />
                                                        </span>
                                                    </Link>
                                                ) : (
                                                    <a className="rbt-btn btn-gradient btn-gradient-2 cursor-pointer w-100 d-block text-center">
                                                        Chờ xác nhận
                                                    </a>
                                                )}
                                            </div>
                                        ) : (
                                            <AddCartCourse courseId={data.documentId} />
                                        )}
                                        <div className="social-share-wrapper mt--30 text-center">
                                            <div className="rbt-post-share d-flex align-items-center justify-content-center">
                                                <SocialLinks className="transparent-with-border justify-content-center" />
                                            </div>
                                            <hr className="mt--20" />
                                            <div className="contact-with-us text-center">
                                                <p>Tìm hiểu về khoá học</p>
                                                <p className="rbt-badge-2 mt--10 justify-content-center w-100">
                                                    <i className="feather-phone mr--5" /> Gọi ngay:{' '}
                                                    <a href={`tel:${config?.phone}`}>
                                                        <strong>{config?.phone}</strong>
                                                    </a>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {!isEmpty(relatedCourses.data) && (
                <>
                    <Separator />
                    <div className="rbt-related-course-area bg-color-white pt--60 rbt-section-gapBottom">
                        <div className="container">
                            <div className="section-title mb--30">
                                <span className="subtitle bg-primary-opacity">Học viện Yêu B & G</span>
                                <h4 className="title">Các khoá học liên quan</h4>
                            </div>
                            <div className="row g-5">
                                {relatedCourses.data?.map((item) => (
                                    <div key={item.id} className="col-lg-4 col-md-6 col-sm-6 col-12">
                                        <CourseItem
                                            item={item}
                                            userId={userId}
                                            bookmarks={bookmarks ?? []}
                                            config={config}
                                        />
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </>
            )}
            <CoursePriceBottom item={data} userCourses={userCourses} />
        </>
    ) : (
        <></>
    );
}

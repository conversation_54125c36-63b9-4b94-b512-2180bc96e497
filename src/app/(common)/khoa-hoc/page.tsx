import Breadcrumb from '@/components/common/Breadcrumb';
import CourseItem from '@/components/common/CourseItem';
import PageTitle from '@/components/common/PageTitle';
import Pagination from '@/components/common/Pagination';
import CourseDisplayType from '@/components/course/CourseDisplayType';
import CourseSearchForm from '@/components/course/CourseSearchForm';
import FormSearch from '@/components/course/FormSearch';
import { getBookmarkCourses } from '@/services/BookmarkCourseService';
import { getCategories } from '@/services/CategoryService';
import { getCourses } from '@/services/CourseService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { SearchCourse } from '@/types/Course';
import { AUTH_KEYS, COURSE_LIMIT, COURSE_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { find, get, includes, isEmpty } from 'lodash';
import { headers } from 'next/headers';

const getPageTitle = (highlight: boolean, isOffline: string, categoryTitle?: string, q?: string) => {
    if (q) return `Tìm kiếm khoá học "${q}"`;
    if (categoryTitle) return categoryTitle;
    if (highlight) return 'Khoá học nổi bật';
    if (includes(['1', '2'], isOffline)) {
        return `Khoá học${isOffline === '1' ? ' tại trung tâm' : ' online trực tuyến'}`;
    }
    return 'Tất cả khoá học';
};

export async function generateMetadata({
    searchParams,
}: {
    searchParams?: Record<string, string | string[] | undefined>;
}) {
    const categoryId = +get(searchParams, 'cId', 0);
    const [config, categories] = await Promise.all([
        getGlobalConfig(),
        categoryId > 0 ? getCategories(true, categoryId) : null,
    ]);
    const siteTitle = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: `${getPageTitle(!!get(searchParams, 'highlight'), (get(searchParams, 'isOffline') ?? '0') as string, categories?.[0]?.title, get(searchParams, 'q')?.toString())} - ${siteTitle}`,
        keywords: !isEmpty(categories) ? `${categories![0].title}, ${config?.keywords}` : (config?.keywords ?? ''),
    };
}

export default async function CourseList({
    searchParams,
}: Readonly<{ searchParams?: Record<string, string | string[] | undefined> }>) {
    const page = +get(searchParams, 'page', 1);
    const searchCourse: SearchCourse = {
        page: page <= 0 ? 1 : page,
        limit: COURSE_LIMIT,
        q: get(searchParams, 'q')?.toString(),
        sortBy: +get(searchParams, 'sortBy', 0),
        categoryId: +get(searchParams, 'cId', 0),
        highlight: !!get(searchParams, 'highlight'),
        isOffline: (get(searchParams, 'isOffline') ?? '0') as string,
        displayList: !!get(searchParams, 'displayList'),
        priceRange: +get(searchParams, 'priceRange', -1),
    };
    const headersList = headers();
    const userId = +(headersList.get(AUTH_KEYS.HEADER_USER_ID) ?? '0');
    const [config, categories, bookmarks, data] = await Promise.all([
        getGlobalConfig(),
        getCategories(),
        getBookmarkCourses(userId),
        getCourses(searchCourse),
    ]);
    const category = searchCourse.categoryId! > 0 ? find(categories, { id: searchCourse.categoryId }) : undefined;
    searchCourse.slug = category?.slug;

    return (
        <>
            <div className="rbt-page-banner-wrapper">
                <div className="rbt-banner-image" />
                <div className="rbt-banner-content">
                    <div className="rbt-banner-content-top">
                        <div className="container">
                            <div className="row">
                                <div className="col-lg-12">
                                    <Breadcrumb
                                        items={
                                            category
                                                ? [
                                                      { id: 1, name: 'Trang chủ', link: '/' },
                                                      { id: 2, name: 'Thể loại', link: '/the-loai' },
                                                      { id: 0, name: category.title, link: '' },
                                                  ]
                                                : [
                                                      { id: 1, name: 'Trang chủ', link: '/' },
                                                      {
                                                          id: 0,
                                                          name: getPageTitle(
                                                              !!searchCourse.highlight,
                                                              searchCourse.isOffline ?? '0',
                                                              undefined,
                                                              searchCourse.q
                                                          ),
                                                          link: '',
                                                      },
                                                  ]
                                        }
                                    />
                                    <PageTitle
                                        title={getPageTitle(
                                            !!searchCourse.highlight,
                                            searchCourse.isOffline ?? '0',
                                            category?.title,
                                            searchCourse.q
                                        )}
                                        description="Khám phá hơn 100 khóa học tại B & G."
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="rbt-course-top-wrapper mt--40 mt_sm--20">
                        <div className="container">
                            <div className="row g-5 align-items-center">
                                <CourseDisplayType searchCourse={searchCourse} courseCount={-1} />
                                <FormSearch searchParams={searchCourse} />
                            </div>
                            <CourseSearchForm categories={categories ?? []} searchParams={searchCourse} />
                        </div>
                    </div>
                </div>
            </div>
            <div
                className={classNames('rbt-section-gapBottom', { 'rbt-section-overlayping-top': !isEmpty(data.data) })}
            >
                <div className="inner">
                    <div className="container">
                        <div
                            className={classNames('rbt-course-grid-column', {
                                'active-list-view': searchCourse.displayList,
                            })}
                        >
                            {(data.data ?? []).map((item) => (
                                <div key={item.id} className="course-grid-3">
                                    <CourseItem
                                        item={item}
                                        userId={userId}
                                        bookmarks={bookmarks ?? []}
                                        config={config}
                                    />
                                </div>
                            ))}
                        </div>
                        {data.meta && (
                            <Pagination
                                baseUrl={COURSE_ROUTE}
                                searchParams={searchCourse}
                                pagination={data.meta.pagination}
                            />
                        )}
                    </div>
                </div>
            </div>
        </>
    );
}

import Breadcrumb from '@/components/common/Breadcrumb';
import CourseItem from '@/components/common/CourseItem';
import PageTitle from '@/components/common/PageTitle';
import Pagination from '@/components/common/Pagination';
import CourseDisplayType from '@/components/course/CourseDisplayType';
import { getBookmarkCourses } from '@/services/BookmarkCourseService';
import { getCourses } from '@/services/CourseService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { getTags } from '@/services/TagService';
import BookmarkCourse from '@/types/BookmarkCourse';
import { ResponseList, Tag } from '@/types/common';
import Course, { SearchCourse } from '@/types/Course';
import { GlobalConfig } from '@/types/GlobalConfig';
import { ARTICLE_LIMIT, AUTH_KEYS, COURSE_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { get, isEmpty, last, split } from 'lodash';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

export async function generateMetadata({ params }: { params: { slug: string } }) {
    let data: Tag[] | null = null,
        config: GlobalConfig | null = null;
    const id = last(split(params.slug, '-'));
    if (id && +id > 0) {
        [data, config] = await Promise.all([getTags(+id), getGlobalConfig()]);
    }
    const siteTitle = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: !isEmpty(data) ? `Tag ${data![0].title} - ${siteTitle}` : siteTitle,
        keywords: !isEmpty(data) ? `${data![0].title}, ${config?.keywords}` : (config?.keywords ?? ''),
    };
}

export default async function TagCourseList({
    params,
    searchParams,
}: {
    params: { slug: string };
    searchParams?: Record<string, string | string[] | undefined>;
}) {
    let tag: Tag | null = null,
        data: ResponseList<Course> = { data: [] },
        config: GlobalConfig | null = null,
        bookmarks: BookmarkCourse[] = [];
    const headersList = headers();
    const userId = +(headersList.get(AUTH_KEYS.HEADER_USER_ID) ?? '0');
    const page = +get(searchParams, 'page', 1);
    const searchCourse: SearchCourse = {
        page: page <= 0 ? 1 : page,
        limit: ARTICLE_LIMIT,
        q: get(searchParams, 'q')?.toString(),
        displayList: !!get(searchParams, 'displayList'),
    };
    const id = last(split(params.slug, '-'));
    if (id && +id > 0) {
        const tags = await getTags(+id);
        if (isEmpty(tags)) notFound();
        else {
            tag = tags![0];
            searchCourse.tagId = tag.id;
            searchCourse.slug = tag.slug;
            [config, data, bookmarks] = await Promise.all([
                getGlobalConfig(),
                getCourses(searchCourse),
                getBookmarkCourses(userId),
            ]);
        }
    } else notFound();

    return tag ? (
        <>
            <div className="rbt-page-banner-wrapper">
                <div className="rbt-banner-image" />
                <div className="rbt-banner-content">
                    <div className="rbt-banner-content-top">
                        <div className="container">
                            <div className="row">
                                <div className="col-lg-12">
                                    <Breadcrumb
                                        items={[
                                            { id: 1, name: 'Trang chủ', link: '/' },
                                            { id: 0, name: `Tag ${tag.title}`, link: '' },
                                        ]}
                                    />
                                    <PageTitle title={`Tag ${tag.title}`} description="Tin tức mới nhất tại B & G" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="rbt-course-top-wrapper mt--40 mt_sm--20">
                        <div className="container">
                            <div className="row g-5 align-items-center">
                                <CourseDisplayType
                                    searchCourse={searchCourse}
                                    courseCount={data.meta?.pagination.total ?? 0}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="rbt-section-overlayping-top rbt-section-gapBottom">
                <div className="inner">
                    <div className="container">
                        <div
                            className={classNames('rbt-course-grid-column', {
                                'active-list-view': searchCourse.displayList,
                            })}
                        >
                            {(data.data ?? []).map((item) => (
                                <div key={item.id} className="course-grid-3">
                                    <CourseItem
                                        item={item}
                                        userId={userId}
                                        bookmarks={bookmarks ?? []}
                                        config={config}
                                    />
                                </div>
                            ))}
                        </div>
                        {data.meta && (
                            <Pagination
                                baseUrl={COURSE_ROUTE}
                                searchParams={searchCourse}
                                pagination={data.meta.pagination}
                            />
                        )}
                    </div>
                </div>
            </div>
        </>
    ) : (
        <></>
    );
}

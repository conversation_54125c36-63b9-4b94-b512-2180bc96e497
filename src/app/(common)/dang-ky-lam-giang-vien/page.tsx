import Breadcrumb from '@/components/common/Breadcrumb';
import { getGlobalConfig } from '@/services/SingleTypeService';

export async function generateMetadata() {
    const config = await getGlobalConfig();
    const title = config?.siteName ?? 'Học viện yêu B & G';
    return {
        title: `Đăng ký làm giảng viên - ${title}`,
    };
}

export default async function BecomeTeacherPage() {
    return (
        <>
            <div className="rbt-breadcrumb-default ptb--100 ptb_md--50 ptb_sm--30 bg-gradient-1">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="breadcrumb-inner text-center">
                                <h2 className="title">Đăng ký làm giảng viên</h2>
                                <Breadcrumb
                                    items={[
                                        { id: 1, name: 'Trang chủ', link: '/' },
                                        { id: 0, name: '<PERSON>ăng ký làm giảng viên', link: '' },
                                    ]}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="rbt-become-area bg-color-white rbt-section-gap">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="section-title text-center">
                                <span className="subtitle bg-pink-opacity">Giảng viên</span>
                                <h2 className="title">Đăng ký làm giảng viên</h2>
                                <p className="description has-medium-font-size mt--20 mb--40">
                                    Lorem ipsum dolor sit amet, consectetur
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="row row row--30">
                        <div className="col-lg-12 mt_md--40 mt_sm--40 order-2 order-lg-1">
                            <div className="advance-tab-button">
                                <ul className="nav nav-tabs tab-button-style-2" id="myTab-4" role="tablist">
                                    <li role="presentation">
                                        <a
                                            href="#"
                                            className="tab-button"
                                            id="home-tab-4"
                                            data-bs-toggle="tab"
                                            data-bs-target="#home-4"
                                            role="tab"
                                            aria-controls="home-4"
                                            aria-selected="false"
                                        >
                                            <span className="title">Become an Intructor.</span>
                                        </a>
                                    </li>
                                    <li role="presentation">
                                        <a
                                            href="#"
                                            className="tab-button active"
                                            id="profile-tab-4"
                                            data-bs-toggle="tab"
                                            data-bs-target="#profile-4"
                                            role="tab"
                                            aria-controls="profile-4"
                                            aria-selected="true"
                                        >
                                            <span className="title">Intructor Rules.</span>
                                        </a>
                                    </li>
                                    <li role="presentation">
                                        <a
                                            href="#"
                                            className="tab-button"
                                            id="contact-tab-4"
                                            data-bs-toggle="tab"
                                            data-bs-target="#contact-4"
                                            role="tab"
                                            aria-controls="contact-4"
                                            aria-selected="false"
                                        >
                                            <span className="title">Start with courses.</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div className="tab-content advance-tab-content-style-2">
                                <div className="tab-pane fade" id="home-4" role="tabpanel" aria-labelledby="home-tab-4">
                                    <div className="content">
                                        <p>
                                            Educational technology ipsum dolor sit amet consectetur, adipisicing elit.
                                            Tempora sequi doloremque dicta quia unde odio nam minus reiciendis ullam
                                            aliquam, dolorum ab quisquam cum numquam nemo iure cumque iste. Accusamus
                                            necessitatibus.
                                        </p>
                                    </div>
                                </div>
                                <div
                                    className="tab-pane fade active show"
                                    id="profile-4"
                                    role="tabpanel"
                                    aria-labelledby="profile-tab-4"
                                >
                                    <div className="content">
                                        <p>
                                            Physical education ipsum dolor sit amet consectetur, adipisicing elit.
                                            Tempora sequi doloremque dicta quia unde odio nam minus reiciendis ullam
                                            aliquam, dolorum ab quisquam cum numquam nemo iure cumque iste. Accusamus
                                            necessitatibus.
                                        </p>
                                    </div>
                                </div>
                                <div
                                    className="tab-pane fade"
                                    id="contact-4"
                                    role="tabpanel"
                                    aria-labelledby="contact-tab-4"
                                >
                                    <div className="content">
                                        <p>
                                            Experiencing music ipsum dolor sit amet consectetur, adipisicing elit.
                                            Tempora sequi doloremque dicta quia unde odio nam minus reiciendis ullam
                                            aliquam, dolorum ab quisquam cum numquam nemo iure cumque iste. Accusamus
                                            necessitatibus.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="row pt--60 g-5">
                        <div className="col-lg-4">
                            <div className="thumbnail">
                                <img
                                    className="radius-10 w-100"
                                    src="assets/images/tab/tabs-10.jpg"
                                    alt="Corporate Template"
                                />
                            </div>
                        </div>
                        <div className="col-lg-8">
                            <div className="rbt-contact-form contact-form-style-1 max-width-auto">
                                <div className="section-title text-start">
                                    <span className="subtitle bg-primary-opacity">For Become a Instructor</span>
                                </div>
                                <h3 className="title">Instructor Registration</h3>
                                <hr className="mb--30" />
                                <form action="#" className="row row--15">
                                    <div className="col-lg-6">
                                        <div className="form-group">
                                            <input name="con_name" type="text" />
                                            <label>First Name</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="form-group">
                                            <input name="con_lastname" type="text" />
                                            <label>Last Name</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="form-group">
                                            <input name="con_username" type="text" />
                                            <label>User name</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="form-group">
                                            <input name="con_phone" type="text" />
                                            <label>Phone Number</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-12">
                                        <div className="form-group">
                                            <input name="con_email" type="email" />
                                            <label>Email</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="form-group">
                                            <input name="con_password" type="password" />
                                            <label>Password</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-6">
                                        <div className="form-group">
                                            <input name="con_passwordconfirm" type="password" />
                                            <label>Password Confirmation</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-12">
                                        <div className="form-group">
                                            <textarea defaultValue={''} />
                                            <label>Bio</label>
                                            <span className="focus-border" />
                                        </div>
                                    </div>
                                    <div className="col-lg-12">
                                        <div className="form-submit-group">
                                            <button
                                                type="submit"
                                                className="rbt-btn btn-md btn-gradient hover-icon-reverse w-100"
                                            >
                                                <span className="icon-reverse-wrapper">
                                                    <span className="btn-text">Đăng ký làm giảng viên</span>
                                                    <span className="btn-icon">
                                                        <i className="feather-arrow-right" />
                                                    </span>
                                                    <span className="btn-icon">
                                                        <i className="feather-arrow-right" />
                                                    </span>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

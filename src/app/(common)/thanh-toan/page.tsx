'use client';

import { getCourses } from '@/services/CourseService';
import { addOrder } from '@/services/OrderService';
import { addUserCourse } from '@/services/UserCourseService';
import { getQRImage } from '@/services/VietQRService';
import { useAuthStore } from '@/stores/authStore';
import { useCartStore } from '@/stores/cartStore';
import Course from '@/types/Course';
import { formatNumber, showToast } from '@/utils';
import { BANKS, MY_BANKS } from '@/utils/banks';
import { API_URL, COMMON_MESSAGE, COURSE_ROUTE, PRODUCT_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { find, get, isEmpty, sumBy, uniq } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { LabelValue } from '@/types/common';
import Product from '@/types/Product';
import { getProducts } from '@/services/ProductService';
import { OrderData, ShippingData } from '@/types/Order';
import { useForm } from 'react-hook-form';
import { District, PROVINCES, Ward } from '@/utils/province';

export default function PaymentPage({
    searchParams,
}: Readonly<{ searchParams?: Record<string, string | string[] | undefined> }>) {
    const [courseIds, setCourseIds] = useState<string[]>([]);
    const [cloneCartProducts, setCloneCartProducts] = useState<LabelValue[]>([]);
    const [chooseCourseIds, setChooseCourseIds] = useState<string[]>([]);
    const [chooseProductIds, setChooseProductIds] = useState<string[]>([]);
    const [courses, setCourses] = useState<Course[]>([]);
    const [products, setProducts] = useState<Product[]>([]);
    const [sumCost, setSumCost] = useState(0);
    const [paymentMethodId, setPaymentMethodId] = useState(0);
    const [paymentImage, setPaymentImage] = useState<string | null>(null);
    const [districts, setDistricts] = useState<District[]>([]);
    const [wards, setWards] = useState<Ward[]>([]);
    const [loading, setLoading] = useState(false);
    const user = useAuthStore((state) => state.user);
    const setShowLogin = useAuthStore((state) => state.setShowLogin);
    const orderCode = useAuthStore((state) => state.orderCode);
    const setOrderCode = useAuthStore((state) => state.setOrderCode);
    const cartCourseIds = useCartStore((state) => state.courseIds);
    const cartProducts = useCartStore((state) => state.products);
    const setCartCourseIds = useCartStore((state) => state.setCourseIds);
    const setCartProducts = useCartStore((state) => state.setProducts);
    const router = useRouter();

    const { register, watch, setValue, handleSubmit } = useForm<ShippingData>();
    const province = watch('province');
    const district = watch('district');

    useEffect(() => {
        const getCartCourses = async (documentIds: string[]) => {
            const response = await getCourses({ documentIds });
            setCourses(response.data ?? []);
        };

        const getCartProducts = async (documentIds: string[]) => {
            const response = await getProducts({ documentIds });
            setProducts(response.data ?? []);
        };

        setCourseIds(cartCourseIds);
        setCloneCartProducts(cartProducts);
        const id = get(searchParams, 'id');
        const pId = get(searchParams, 'pid');
        if (!isEmpty(cartCourseIds)) {
            getCartCourses(uniq(cartCourseIds));
            if (id && cartCourseIds.includes(id.toString())) {
                setChooseCourseIds([id.toString()]);
            } else if (!pId) {
                setChooseCourseIds(cartCourseIds);
            }
        }
        if (!isEmpty(cartProducts)) {
            const pIds = cartProducts.map((item) => item.label);
            getCartProducts(uniq(pIds));
            if (pId && pIds.includes(pId.toString())) {
                setChooseProductIds([pId.toString()]);
            } else if (!id) {
                setChooseProductIds(pIds);
            }
        }
    }, [cartCourseIds, cartProducts, searchParams]);

    useEffect(() => {
        setSumCost(
            sumBy(courses, (item) => (chooseCourseIds.includes(item.documentId) ? item.sale_price : 0)) +
                sumBy(products, (item) =>
                    chooseProductIds.includes(item.documentId)
                        ? item.sale_price * (find(cloneCartProducts, (cp) => cp.label === item.documentId)?.value ?? 1)
                        : 0
                )
        );
    }, [courses, products, chooseCourseIds, chooseProductIds, cloneCartProducts]);

    useEffect(() => {
        const genImage = async (pmId: number) => {
            const acqId = find(BANKS, { id: pmId })?.bin ?? '';
            const bank = find(MY_BANKS, { id: pmId });
            setLoading(true);
            const response = await getQRImage({
                accountNo: bank?.accountNo ?? '',
                accountName: bank?.accountName ?? '',
                acqId,
                amount: sumCost,
                addInfo: orderCode,
                format: 'text',
                template: 'print',
            });
            setLoading(false);
            if (response.code === '00') {
                setPaymentImage(response.data?.qrDataURL ?? null);
            } else setPaymentImage(null);
        };
        if (sumCost > 0 && paymentMethodId > 0 && user) {
            genImage(paymentMethodId);
        } else setPaymentImage(null);
    }, [sumCost, paymentMethodId, user, orderCode]);

    useEffect(() => {
        setValue('district', '');
        setValue('ward', '');
        if (province) {
            setDistricts(find(PROVINCES, { name: province })?.districts ?? []);
        } else setDistricts([]);
        setWards([]);
    }, [province, setValue]);

    useEffect(() => {
        setValue('ward', '');
        if (district) {
            setWards(find(districts, { name: district })?.wards ?? []);
        } else setWards([]);
    }, [district, districts, setValue]);

    const onChooseItem = (id: string, checked: boolean, isCourse: boolean) => {
        if (isCourse) {
            if (checked) {
                setChooseCourseIds([...chooseCourseIds, id]);
            } else {
                setChooseCourseIds([...chooseCourseIds].filter((item) => item !== id));
            }
        } else {
            if (checked) {
                setChooseProductIds([...chooseProductIds, id]);
            } else {
                setChooseProductIds([...chooseProductIds].filter((item) => item !== id));
            }
        }
    };

    const onChangeQuantity = (value: string, id: string) => {
        if (/^\d*$/.test(value)) {
            const quantity = value === '' ? 1 : +value > 99 ? 99 : +value;
            const items = [...cloneCartProducts];
            const index = items.findIndex((item) => item.label === id);
            if (index !== -1) {
                items[index].value = quantity;
                setCloneCartProducts(items);
                setCartProducts(items);
            }
        }
    };

    const onAdjustQuantity = (id: string, flag: boolean) => {
        const items = [...cloneCartProducts];
        const index = items.findIndex((item) => item.label === id);
        if (index !== -1) {
            let value = items[index].value;
            if (flag) {
                value = value >= 99 ? 99 : value + 1;
            } else {
                value = value <= 1 ? 1 : value - 1;
            }
            items[index].value = value;
            setCloneCartProducts(items);
            setCartProducts(items);
        }
    };

    const removeCart = (id: string, isCourse: boolean) => {
        if (isCourse && courseIds.includes(id)) {
            const items = [...courseIds].filter((item) => item !== id);
            setCourseIds(items);
            setCartCourseIds(items);
            setCourses([...courses].filter((item) => item.documentId !== id));
            showToast(true, COMMON_MESSAGE.DELETE_CART);
        }
        if (!isCourse && cloneCartProducts.map((item) => item.label).includes(id)) {
            const items = [...cloneCartProducts].filter((item) => item.label !== id);
            setCloneCartProducts(items);
            setCartProducts(items);
            setProducts([...products].filter((item) => item.documentId !== id));
            showToast(true, COMMON_MESSAGE.DELETE_CART);
        }
    };

    const onChangePaymentMethod = (pmId: number, checked: boolean) => {
        if (checked) {
            setPaymentMethodId(pmId);
        }
    };

    const onCheckout = async (shipping: ShippingData) => {
        if (paymentMethodId === 0) {
            showToast(false, 'Vui lòng chọn phương thức thanh toán');
            return;
        }
        if (!isEmpty(chooseProductIds)) {
            for (const key in shipping) {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                if (key !== 'comment' && isEmpty(shipping[key])) {
                    showToast(false, 'Vui lòng điền đầy đủ thông tin về địa chỉ giao hàng');
                    return;
                }
            }
        }
        const orderData: OrderData[] = courses
            .filter((item) => chooseCourseIds.includes(item.documentId))
            .map((item) => ({
                id: item.documentId,
                price: item.sale_price,
                quantity: 1,
                isCourse: true,
            }));
        products
            .filter((item) => chooseProductIds.includes(item.documentId))
            .forEach((item) => {
                orderData.push({
                    id: item.documentId,
                    price: item.sale_price,
                    quantity: find(cloneCartProducts, (cp) => cp.label === item.documentId)?.value ?? 1,
                    isCourse: false,
                });
            });
        const response = await addOrder({
            user_id: user!.id,
            price: sumBy(courses, (item) => (chooseCourseIds.includes(item.documentId) ? item.sale_price : 0)),
            payment_method_id: paymentMethodId,
            code: orderCode,
            approved: false,
            data: JSON.stringify(orderData),
            shipping: JSON.stringify(shipping),
        });
        if (response.data) {
            const orderId = response.data.id!;
            for (const item1 of courses.filter((item) => chooseCourseIds.includes(item.documentId))) {
                await addUserCourse({
                    user_id: user!.id,
                    course_id: item1.id,
                    price: item1.sale_price,
                    approved: false,
                    order_id: orderId,
                });
            }
            showToast(true, 'Đặt hàng thành công');
            setCartCourseIds(
                courses.filter((item) => !chooseCourseIds.includes(item.documentId)).map((item) => item.documentId)
            );
            setCartProducts(
                products
                    .filter((item) => !chooseProductIds.includes(item.documentId))
                    .map((item) => ({
                        label: item.documentId,
                        value: find(cloneCartProducts, (cp) => cp.label === item.documentId)?.value ?? 1,
                    }))
            );
            setOrderCode(user);
            router.push('/don-hang');
        } else showToast(false, COMMON_MESSAGE.ERROR_MESSAGE);
    };

    return (
        <>
            <div className="rbt-breadcrumb-default ptb--100 ptb_md--50 ptb_sm--30 bg-gradient-1">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="breadcrumb-inner text-center">
                                <h2 className="title">Thanh toán</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="checkout_area bg-color-white rbt-section-gap">
                <div className="container">
                    <form className="row g-5 checkout-form" method="POST" onSubmit={handleSubmit(onCheckout)}>
                        <div className="col-lg-7">
                            <div className="cart-table table-responsive mb--60">
                                <table className="table">
                                    <thead>
                                        <tr>
                                            <th className="pro-choose">Chọn</th>
                                            <th className="pro-thumbnail">Ảnh</th>
                                            <th className="pro-title">Khoá học</th>
                                            <th className="pro-price">Giá</th>
                                            <th className="pro-remove">Xoá</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {courses.map((item) => (
                                            <tr key={item.id}>
                                                <td className="pro-choose">
                                                    <input
                                                        type="checkbox"
                                                        value={item.documentId}
                                                        checked={chooseCourseIds.includes(item.documentId)}
                                                        onChange={(e) =>
                                                            onChooseItem(e.target.value, e.target.checked, true)
                                                        }
                                                    />
                                                </td>
                                                <td className="pro-thumbnail">
                                                    <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                                                        <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                                                    </Link>
                                                </td>
                                                <td className="pro-title">
                                                    <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                                                        {item.title}
                                                    </Link>
                                                </td>
                                                <td className="pro-price">
                                                    <span>{formatNumber(item.sale_price)}đ</span>
                                                </td>
                                                <td className="pro-remove">
                                                    <a
                                                        className="cursor-pointer"
                                                        onClick={() => removeCart(item.documentId, true)}
                                                    >
                                                        <i className="feather-x" />
                                                    </a>
                                                </td>
                                            </tr>
                                        ))}
                                        {products.map((item) => (
                                            <tr key={item.id}>
                                                <td className="pro-choose">
                                                    <input
                                                        type="checkbox"
                                                        value={item.documentId}
                                                        checked={chooseProductIds.includes(item.documentId)}
                                                        onChange={(e) =>
                                                            onChooseItem(e.target.value, e.target.checked, false)
                                                        }
                                                    />
                                                </td>
                                                <td className="pro-thumbnail">
                                                    <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>
                                                        <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                                                    </Link>
                                                </td>
                                                <td className="pro-title">
                                                    <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>
                                                        {item.title}
                                                    </Link>
                                                </td>
                                                <td className="pro-quantity">
                                                    <div className="pro-qty">
                                                        <span
                                                            className="dec qtybtn"
                                                            onClick={() => onAdjustQuantity(item.documentId, false)}
                                                        >
                                                            -
                                                        </span>
                                                        <input
                                                            type="text"
                                                            className="pro-qty"
                                                            value={
                                                                find(
                                                                    cloneCartProducts,
                                                                    (cp) => cp.label === item.documentId
                                                                )?.value ?? 1
                                                            }
                                                            onChange={(e) =>
                                                                onChangeQuantity(e.target.value, item.documentId)
                                                            }
                                                        />
                                                        <span
                                                            className="inc qtybtn"
                                                            onClick={() => onAdjustQuantity(item.documentId, true)}
                                                        >
                                                            +
                                                        </span>
                                                    </div>

                                                    <span>{formatNumber(item.sale_price)}đ</span>
                                                </td>
                                                <td className="pro-remove">
                                                    <a
                                                        className="cursor-pointer"
                                                        onClick={() => removeCart(item.documentId, false)}
                                                    >
                                                        <i className="feather-x" />
                                                    </a>
                                                </td>
                                            </tr>
                                        ))}
                                        <tr>
                                            <td colSpan={2}></td>
                                            <td className="pro-title">
                                                <span className="color-primary order-code">Tổng tiền</span>
                                            </td>
                                            <td className="pro-price">
                                                <span className="theme-gradient">{formatNumber(sumCost)}đ</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            {!isEmpty(chooseProductIds) && (
                                <div id="billing-form">
                                    <h4 className="checkout-title">Địa chỉ giao hàng</h4>
                                    <div className="rbt-lost-password mb--30">
                                        <p className="w-400 order-code">
                                            Shop B & G sẽ che người nhận và thông tin sản phẩm trên bưu kiện giao hàng
                                        </p>
                                    </div>
                                    <div className="row">
                                        <div className="col-md-6 col-12 mb--20">
                                            <label>Họ và tên *</label>
                                            <input {...register('name')} type="text" />
                                        </div>
                                        <div className="col-md-6 col-12 mb--20">
                                            <label>Số điện thoại *</label>
                                            <input {...register('phone')} type="text" />
                                        </div>
                                        <div className="col-md-4 col-12 mb--20">
                                            <label>Tỉnh *</label>
                                            <div className="rbt-modern-select bg-transparent height-45">
                                                <select {...register('province')} className="w-100">
                                                    <option value="">--Chọn--</option>
                                                    {PROVINCES.map((item, index) => (
                                                        <option key={index} value={item.name}>
                                                            {item.name}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>
                                        <div className="col-md-4 col-12 mb--20">
                                            <label>Huyện *</label>
                                            <div className="rbt-modern-select bg-transparent height-45">
                                                <select {...register('district')} className="w-100">
                                                    <option value="">--Chọn--</option>
                                                    {districts.map((item, index) => (
                                                        <option key={index} value={item.name}>
                                                            {item.name}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>
                                        <div className="col-md-4 col-12 mb--20">
                                            <label>Xã *</label>
                                            <div className="rbt-modern-select bg-transparent height-45">
                                                <select {...register('ward')} className="w-100">
                                                    <option value="">--Chọn--</option>
                                                    {wards.map((item, index) => (
                                                        <option key={index} value={item.name}>
                                                            {item.name}
                                                        </option>
                                                    ))}
                                                </select>
                                            </div>
                                        </div>
                                        <div className="col-12 mb--20">
                                            <label>Địa chỉ *</label>
                                            <input {...register('address')} type="text" />
                                        </div>
                                        <div className="col-12 mb--20">
                                            <label>Ghi chú</label>
                                            <input {...register('comment')} type="text" />
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="col-lg-5">
                            <div className="row pl--50 pl_md--0 pl_sm--0">
                                <div className="col-12 mb--60">
                                    <div className="checkout-cart-total">
                                        <p>
                                            Tổng tiền hàng <span>{formatNumber(sumCost)}đ</span>
                                        </p>
                                        <p>
                                            Phí vận chuyển <span>0đ</span>
                                        </p>
                                        <p style={{ fontSize: '20px' }}>
                                            Tổng tiền <span className="theme-gradient">{formatNumber(sumCost)}đ</span>
                                        </p>
                                    </div>
                                </div>
                                <div className="col-12 mb--60">
                                    <h4 className="checkout-title">Phương thức thanh toán</h4>
                                    <div className="checkout-payment-method">
                                        <p className="order-code">
                                            Nội dung chuyển khoản: <span className="color-primary">{orderCode}</span>
                                        </p>
                                        {BANKS.filter((item) => MY_BANKS.map((b) => b.id).includes(item.id)).map(
                                            (item) => (
                                                <div className="single-method" key={item.id}>
                                                    <input
                                                        type="radio"
                                                        id={`payment_${item.id}`}
                                                        name="payment-method"
                                                        value={item.id}
                                                        checked={item.id === paymentMethodId}
                                                        onChange={(e) =>
                                                            onChangePaymentMethod(+e.target.value, e.target.checked)
                                                        }
                                                    />
                                                    <label htmlFor={`payment_${item.id}`}>
                                                        {item.name} ({item.code})
                                                    </label>
                                                    <p className={classNames({ hidden: item.id !== paymentMethodId })}>
                                                        {paymentImage && !loading && (
                                                            <img
                                                                src={paymentImage}
                                                                alt={item.shortName}
                                                                className="mt--5 w-100"
                                                            />
                                                        )}
                                                        {loading && <img src="/assets/images/loading.gif" alt="" />}
                                                    </p>
                                                </div>
                                            )
                                        )}
                                        {/* <div className="single-method">
                                            <input type="checkbox" id="accept_terms" />
                                            <label htmlFor="accept_terms">Xác nhận đã thanh toán</label>
                                        </div> */}
                                    </div>
                                    <div className="rbt-lost-password mt--30">
                                        <p className="w-400 order-code">
                                            Đơn hàng sẽ tự động cập nhật trạng thái sau khi thanh toán
                                        </p>
                                    </div>
                                    {!user && (
                                        <div className="rbt-lost-password mt--30">
                                            <a
                                                className="rbt-btn-link w-400 cursor-pointer"
                                                onClick={() => setShowLogin(true)}
                                            >
                                                Bạn cần đăng nhập trước khi thanh toán
                                            </a>
                                        </div>
                                    )}
                                    <div className="plceholder-button mt--30">
                                        <button
                                            className="rbt-btn btn-gradient hover-icon-reverse"
                                            type="submit"
                                            disabled={!user || sumCost === 0}
                                            //onClick={onCheckout}
                                        >
                                            <span className="icon-reverse-wrapper">
                                                <span className="btn-text">Mua Hàng</span>
                                                <span className="btn-icon">
                                                    <i className="feather-arrow-right" />
                                                </span>
                                                <span className="btn-icon">
                                                    <i className="feather-arrow-right" />
                                                </span>
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </>
    );
}

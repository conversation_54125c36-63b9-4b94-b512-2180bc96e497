import BlogItem from '@/components/blog/BlogItem';
import BlogShortList from '@/components/blog/BlogShortList';
import FormSearch from '@/components/blog/FormSearch';
import Breadcrumb from '@/components/common/Breadcrumb';
import PageTitle from '@/components/common/PageTitle';
import Pagination from '@/components/common/Pagination';
import Tags from '@/components/common/Tags';
import { getArticles } from '@/services/ArticleService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { getTags } from '@/services/TagService';
import { SearchArticle } from '@/types/Article';
import { ARTICLE_LIMIT, ARTICLE_ROUTE } from '@/utils/constants';
import { get } from 'lodash';

export async function generateMetadata() {
    const config = await getGlobalConfig();
    const title = config?.siteName ?? 'Họ<PERSON> viện yêu B & G';
    return {
        title: `Tin tức - ${title}`,
    };
}

export default async function ArticleList({
    searchParams,
}: Readonly<{ searchParams?: Record<string, string | string[] | undefined> }>) {
    const page = +get(searchParams, 'page', 1);
    const searchArticle: SearchArticle = {
        page: page <= 0 ? 1 : page,
        limit: ARTICLE_LIMIT,
        isPage: false,
        q: get(searchParams, 'q')?.toString(),
    };
    const [data, tags, lastedNews] = await Promise.all([
        getArticles(searchArticle),
        getTags(),
        getArticles({ isPage: false, limit: ARTICLE_LIMIT }),
    ]);

    return (
        <>
            <div className="rbt-page-banner-wrapper">
                <div className="rbt-banner-image" />
                <div className="rbt-banner-content">
                    <div className="rbt-banner-content-top">
                        <div className="container">
                            <div className="row">
                                <div className="col-lg-12">
                                    <Breadcrumb
                                        items={[
                                            { id: 1, name: 'Trang chủ', link: '/' },
                                            { id: 0, name: 'Tin tức', link: '' },
                                        ]}
                                    />
                                    <PageTitle title="Tin tức" description="Tin tức mới nhất tại B & G" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="rbt-section-overlayping-top rbt-section-gapBottom">
                <div className="container">
                    <div className="row row--30 gy-5">
                        <div className="col-lg-8">
                            <div className="row g-5">
                                {(data.data ?? []).map((item) => (
                                    <BlogItem key={item.id} item={item} />
                                ))}
                            </div>
                            {data.meta && (
                                <Pagination
                                    baseUrl={ARTICLE_ROUTE}
                                    searchParams={searchArticle}
                                    pagination={data.meta.pagination}
                                />
                            )}
                        </div>
                        <div className="col-lg-4">
                            <aside className="rbt-sidebar-widget-wrapper rbt-gradient-border">
                                <FormSearch searchParams={searchArticle} />
                                <BlogShortList items={lastedNews.data ?? []} />
                                <div className="rbt-single-widget rbt-widget-tag">
                                    <div className="inner">
                                        <h4 className="rbt-widget-title">Tags</h4>
                                        <div className="rbt-sidebar-list-wrapper rbt-tag-list">
                                            <Tags tags={tags ?? []} />
                                        </div>
                                    </div>
                                </div>
                            </aside>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

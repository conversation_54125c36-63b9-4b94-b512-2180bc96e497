import BackToTop from '@/components/common/BackToTop';
import Header from '@/components/common/Header';
import Separator from '@/components/common/Separator';
import SocialLinks from '@/components/common/SocialLinks';
import { getCategories } from '@/services/CategoryService';
import { getCourses } from '@/services/CourseService';
import { getGlobalConfig } from '@/services/SingleTypeService';
import { API_URL, ARTICLE_ROUTE, COURSE_ROUTE, PRODUCT_ROUTE } from '@/utils/constants';
import Link from 'next/link';
import React from 'react';

export default async function CommonLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    const [config, categories, pCategories, highlightCourses] = await Promise.all([
        getGlobalConfig(),
        getCategories(),
        getCategories(false),
        getCourses({ highlight: true }),
    ]);

    return (
        <>
            <Header
                config={config}
                categories={categories ?? []}
                pCategories={pCategories ?? []}
                highlightCourses={highlightCourses.data ?? []}
            />
            {children}
            <Separator />
            <footer className="rbt-footer footer-style-1">
                <div className="footer-top">
                    <div className="container">
                        <div className="row row--15 mt_dec--30">
                            <div className="col-lg-3 col-md-6 col-sm-6 col-12 mt--30">
                                <div className="footer-widget">
                                    <div className="logo">
                                        <Link href="/">
                                            <img
                                                src={`${API_URL}${config?.logoFooter.url}`}
                                                alt={config?.siteName}
                                                className="logo-footer"
                                            />
                                        </Link>
                                    </div>
                                </div>
                            </div>
                            <div className="col-lg-3 col-md-6 col-sm-6 col-12 mt--30">
                                <div className="footer-widget">
                                    <h5 className="ft-title">Khoá học</h5>
                                    <ul className="ft-link">
                                        <li>
                                            <Link href={COURSE_ROUTE}>Tất cả khoá học</Link>
                                        </li>
                                        <li>
                                            <Link href="/khoa-hoc-noi-bat">Khoá học nổi bật</Link>
                                        </li>
                                        <li>
                                            <Link href="/khoa-hoc-online-truc-tuyen">Khoá học online trực tuyến</Link>
                                        </li>
                                        <li>
                                            <Link href="/khoa-hoc-tai-trung-tam">Khoá học tại trung tâm</Link>
                                        </li>
                                        <li>
                                            <Link href="/huong-dan">Hướng dẫn sử dụng</Link>
                                        </li>
                                        <li>
                                            <Link href="/kich-hoat">Kích hoạt khoá học</Link>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div className="col-lg-3 col-md-6 col-sm-6 col-12 mt--30">
                                <div className="footer-widget">
                                    <h5 className="ft-title">Về B & G</h5>
                                    <ul className="ft-link">
                                        <li>
                                            <Link href="/ve-chung-toi">Giới thiệu về B & G</Link>
                                        </li>
                                        <li>
                                            <Link href="/lien-he">Liên hệ</Link>
                                        </li>
                                        <li>
                                            <Link href={ARTICLE_ROUTE}>Tin tức</Link>
                                        </li>
                                        <li>
                                            <Link href="/faqs">FAQs</Link>
                                        </li>
                                        {/*<li>*/}
                                        {/*    <Link href="/dang-ky-lam-giang-vien">Đăng ký làm giảng viên</Link>*/}
                                        {/*</li>*/}
                                        <li>
                                            <Link href={PRODUCT_ROUTE}>Sản phẩm</Link>
                                        </li>
                                        <li>
                                            <Link href="/goi-dang-ky">Gói đăng ký</Link>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div className="col-lg-3 col-md-6 col-sm-6 col-12 mt--30">
                                <div className="footer-widget">
                                    <h5 className="ft-title">Liên hệ</h5>
                                    <ul className="ft-link">
                                        <li>
                                            <span>Điện thoại:</span>{' '}
                                            <a href={`tel:${config?.phone}`}>{config?.phone}</a>
                                        </li>
                                        <li>
                                            <span>Email:</span> <a href={`mailto:${config?.email}`}>{config?.email}</a>
                                        </li>
                                        <li>
                                            <span>Địa chỉ:</span> {config?.address}
                                        </li>
                                        <li>08h00 - 17h30, thứ 2 - thứ 7</li>
                                    </ul>
                                    <SocialLinks className="icon-naked justify-content-start mt--20" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <Separator />
            <div className="copyright-area copyright-style-1 ptb--20">
                <div className="container">
                    <div className="row align-items-center">
                        <div className="col-xxl-6 col-xl-6 col-lg-6 col-md-12 col-12">
                            <p className="rbt-link-hover text-center text-lg-start">{config?.copyright}</p>
                        </div>
                        <div className="col-xxl-6 col-xl-6 col-lg-6 col-md-12 col-12">
                            <ul className="copyright-link rbt-link-hover justify-content-center justify-content-lg-end mt_sm--10 mt_md--10">
                                <li>
                                    <Link href="/page/dieu-khoan-dich-vu">Điều khoản dịch vụ</Link>
                                </li>
                                <li>
                                    <Link href="/page/chinh-sach-bao-mat">Chính sách bảo mật</Link>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <BackToTop />
        </>
    );
}

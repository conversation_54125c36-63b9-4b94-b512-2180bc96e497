'use client';

import CountUp from 'react-countup';
import VisibilitySensor from 'react-visibility-sensor';

export default function CounterUp({ count }: Readonly<{ count: number }>) {
    return (
        <CountUp end={count}>
            {({ countUpRef, start }) => (
                <VisibilitySensor onChange={start}>
                    <span className="odometer" ref={countUpRef} />
                </VisibilitySensor>
            )}
        </CountUp>
    );
}

'use client';

import classNames from 'classnames';
import { useState } from 'react';

interface IProps {
    showRegister: boolean;
    setShowRegister: (s: boolean) => void;
}

export default function TopPromotion({ showRegister, setShowRegister }: Readonly<IProps>) {
    const [show, setShow] = useState(true);

    return (
        <div
            className={classNames('rbt-header-campaign rbt-header-campaign-1 rbt-header-top-news bg-image1', {
                hidden: !show,
            })}
        >
            <div className="wrapper">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="inner justify-content-center">
                                <div className="content">
                                    <span className="rbt-badge variation-02 bg-color-primary color-white radius-round">
                                        Khuyến mại chỉ còn trong hôm nay
                                    </span>
                                    <span className="news-text color-white-off">
                                        <img src="/assets/images/icons/hand-emojji.svg" alt="" /> Giảm 50% tất cả các
                                        khoá học online. Nhanh tay đăng ký.
                                    </span>
                                </div>
                                {showRegister && (
                                    <div className="right-button">
                                        <a
                                            className="rbt-btn-link color-white cursor-pointer"
                                            onClick={() => setShowRegister(true)}
                                        >
                                            <span>
                                                Đăng ký ngay <i className="feather-arrow-right" />
                                            </span>
                                        </a>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className="icon-close position-right">
                <button className="rbt-round-btn btn-white-off bgsection-activation" onClick={() => setShow(false)}>
                    <i className="feather-x" />
                </button>
            </div>
        </div>
    );
}

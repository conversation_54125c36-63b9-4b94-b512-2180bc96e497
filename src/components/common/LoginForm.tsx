'use client';

import { login } from '@/services/UserService';
import { useAuthStore } from '@/stores/authStore';
import User from '@/types/User';
import { showToast } from '@/utils';
import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { COMMON_MESSAGE } from '@/utils/constants';

interface IProps {
    show: boolean;
    setShow: (s: boolean) => void;
    setShowRegister: (s: boolean) => void;
}
export default function LoginForm({ show, setShow, setShowRegister }: Readonly<IProps>) {
    const setUser = useAuthStore((state) => state.setUser);

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors, isSubmitting },
    } = useForm<User>({
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        resolver: yupResolver(
            yup
                .object({
                    email: yup
                        .string()
                        .required(COMMON_MESSAGE.FIELD_REQUIRED)
                        .trim()
                        .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, COMMON_MESSAGE.FIELD_EMAIL),
                    password: yup
                        .string()
                        .required(COMMON_MESSAGE.FIELD_REQUIRED)
                        .trim()
                        .min(6, COMMON_MESSAGE.FIELD_PASSWORD_LENGTH),
                })
                .required()
        ),
    });

    const onSubmit = async (data: User) => {
        const response = await login(data.email, data.password);
        if (response.user) {
            setUser({ ...response.user, jwt: response.jwt });
            reset({ username: '', password: '' });
            showToast(true, 'Đăng nhập thành công');
            setShow(false);
        } else showToast(false, COMMON_MESSAGE.ERROR_MESSAGE);
    };

    return (
        <div className={classNames('rbt-cart-side-menu', { 'side-menu-active': show })}>
            <div className="inner-wrapper" style={{ display: 'block' }}>
                <div className="inner-top">
                    <div className="content">
                        <div className="title">
                            <h4 className="title mb--0">Đăng nhập</h4>
                        </div>
                        <div className="rbt-btn-close">
                            <button className="minicart-close-button rbt-round-btn" onClick={() => setShow(false)}>
                                <i className="feather-x" />
                            </button>
                        </div>
                    </div>
                </div>
                <div className="side-nav w-100">
                    <form className="w-90p" method="POST" onSubmit={handleSubmit(onSubmit)}>
                        <div className="form-group focused">
                            <label className={classNames({ 'color-danger': !!errors.email?.message })}>Email</label>
                            <input {...register('email')} type="email" />
                            <span className="focus-border" />
                            <label className="color-danger text-right">{errors.email?.message}</label>
                        </div>
                        <div className="form-group focused">
                            <label className={classNames({ 'color-danger': !!errors.password?.message })}>
                                Mật khẩu
                            </label>
                            <input {...register('password')} type="password" />
                            <span className="focus-border" />
                            <label className="color-danger text-right">{errors.password?.message}</label>
                        </div>
                        <div className="rbt-lost-password mb--30">
                            <a className="rbt-btn-link w-400" href="#">
                                Quên mật khẩu
                            </a>
                        </div>
                        <div className="form-submit-group">
                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className="rbt-btn btn-md btn-gradient hover-icon-reverse w-100"
                            >
                                <span className="icon-reverse-wrapper">
                                    <span className="btn-text">Đăng nhập</span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right" />
                                    </span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right" />
                                    </span>
                                </span>
                            </button>
                        </div>
                        <div className="rbt-lost-password mb--30">
                            <a
                                className="rbt-btn-link w-400 cursor-pointer"
                                onClick={() => {
                                    setShow(false);
                                    setShowRegister(true);
                                }}
                            >
                                Chưa có tài khoản, Đăng ký ngay
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}

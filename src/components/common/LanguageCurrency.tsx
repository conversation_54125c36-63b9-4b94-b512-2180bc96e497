export default function LanguageCurrency() {
    return (
        <div className="rbt-header-sec-col rbt-header-left">
            <div className="rbt-header-content">
                <div className="header-info">
                    <ul className="rbt-dropdown-menu switcher-language">
                        <li className="has-child-menu">
                            <a href="#">
                                <img className="left-image" src="/assets/images/icons/en-us.png" alt="" />
                                <span className="menu-item">Vietnamese</span>
                                {/* <i className="right-icon feather-chevron-down" /> */}
                            </a>
                            {/* <ul className="sub-menu">
                        <li>
                            <a href="#">
                                <img
                                    className="left-image"
                                    src="/assets/images/icons/fr.png"
                                    alt="Language Images"
                                />
                                <span className="menu-item">Français</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <img
                                    className="left-image"
                                    src="/assets/images/icons/de.png"
                                    alt="Language Images"
                                />
                                <span className="menu-item">Deutsch</span>
                            </a>
                        </li>
                    </ul> */}
                        </li>
                    </ul>
                </div>
                <div className="header-info">
                    <ul className="rbt-dropdown-menu currency-menu">
                        <li className="has-child-menu">
                            <a href="#">
                                <span className="menu-item">VND</span>
                                {/* <i className="right-icon feather-chevron-down" /> */}
                            </a>
                            {/* <ul className="sub-menu hover-reverse">
                        <li>
                            <a href="#">
                                <span className="menu-item">EUR</span>
                            </a>
                        </li>
                        <li>
                            <a href="#">
                                <span className="menu-item">GBP</span>
                            </a>
                        </li>
                    </ul> */}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    );
}

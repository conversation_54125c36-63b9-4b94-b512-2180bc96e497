import { getVideoOtp } from '@/services/VdocipherService';
import Link from 'next/link';

interface IProps {
    showButton?: boolean;
}

export default async function HomeAbout({ showButton }: Readonly<IProps>) {
    const otpVideo = await getVideoOtp('625160806eb24ab3a6428c0583442d18');

    return (
        <div className="rbt-about-area bg-color-white rbt-section-gap pb_md--80 pb_sm--80 about-style-1">
            <div className="container">
                <div className="row g-5 align-items-center">
                    <div className="col-lg-6">
                        <div className="thumbnail-wrapper">
                            {otpVideo ? (
                                <iframe
                                    src={`https://player.vdocipher.com/v2/?otp=${otpVideo.otp}&playbackInfo=${otpVideo.playbackInfo}`}
                                    style={{ border: 0, height: '360px', width: '640px', maxWidth: '100%' }}
                                    allowFullScreen={true}
                                    allow="encrypted-media"
                                />
                            ) : (
                                <video width="100%" autoPlay muted>
                                    <source
                                        src="https://assets-global.website-files.com/6374f206c3d120beb4f3138d/642c1891cc98cfeb6c616ca8_Beducated_HP_Video_20230404_2-transcode.mp4"
                                        type="video/mp4"
                                    />
                                    Your browser does not support the video tag.
                                </video>
                            )}
                        </div>
                    </div>
                    <div className="col-lg-6">
                        <div className="inner pl--50 pl_sm--0 pl_md--0">
                            <div className="section-title text-start">
                                <span className="subtitle bg-coral-opacity">Học viện Yêu B & G</span>
                                <h2 className="title">
                                    Giáo dục giới tính
                                    <br />
                                    bài bản chưa từng có
                                </h2>
                            </div>
                            <p className="description mt--30">
                                B & G đã tạo ra nền tảng này để mang giáo dục giới tính dựa trên niềm vui. Tầm nhìn của
                                chúng tôi? Đập tan những điều cấm kỵ về tình dục mãi mãi..
                            </p>
                            <div className="rbt-feature-wrapper mt--20 ml_dec_20">
                                <div className="rbt-feature feature-style-2 rbt-radius">
                                    <div className="icon bg-pink-opacity">
                                        <i className="feather-heart"></i>
                                    </div>
                                    <div className="feature-content">
                                        <h6 className="feature-title">Không xấu hổ. Giáo dục. An toàn</h6>
                                        <p className="feature-description">
                                            Đây là <strong>không gian an toàn</strong> cho tất cả mọi người, bất kể tình
                                            trạng mối quan hệ, khuynh hướng tình dục hay giới tính.
                                        </p>
                                    </div>
                                </div>
                                <div className="rbt-feature feature-style-2 rbt-radius">
                                    <div className="icon bg-primary-opacity">
                                        <i className="feather-book"></i>
                                    </div>
                                    <div className="feature-content">
                                        <h6 className="feature-title">Học mọi lúc, mọi nơi, suốt đời</h6>
                                        <p className="feature-description">
                                            Bạn có thể <strong>học mọi lúc, mọi nơi</strong> vào bất kỳ thời gian nào,
                                            chỉ cần một chiếc máy tính, điện thoại, tablet có kết nối internet.
                                            <br />
                                            Sở hữu, truy cập khoá học <strong>suốt đời</strong> chỉ cần mua 1 lần
                                        </p>
                                    </div>
                                </div>
                            </div>
                            {showButton && (
                                <div className="about-btn mt--40">
                                    <Link className="rbt-btn btn-gradient hover-icon-reverse" href="/ve-chung-toi">
                                        <span className="icon-reverse-wrapper">
                                            <span className="btn-text">Về chúng tôi</span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right"></i>
                                            </span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right"></i>
                                            </span>
                                        </span>
                                    </Link>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

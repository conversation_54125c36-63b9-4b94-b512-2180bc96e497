import classNames from 'classnames';

interface IProps {
    className: string;
}

export default function SocialLinks({ className }: Readonly<IProps>) {
    return (
        <ul className={classNames('social-icon social-default', className)}>
            <li>
                <a href="https://facebook.com/hocvienyeubg" target="_blank">
                    <i className="feather-facebook" />
                </a>
            </li>
            <li>
                <a href="https://www.youtube.com/@hocvienyeu-bg" target="_blank">
                    <i className="feather-youtube" />
                </a>
            </li>
            <li>
                <a href="https://www.tiktok.com/@hocvienyeu" target="_blank">
                    <i className="feather-instagram" />
                </a>
            </li>
        </ul>
    );
}

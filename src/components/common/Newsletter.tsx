'use client';

import { addSubscribe } from '@/services/SubscribeService';
import { GlobalConfig } from '@/types/GlobalConfig';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import CounterUp from './CounterUp';
import classNames from 'classnames';
import { COMMON_MESSAGE, GG_RECAPTCHA_KEY } from '@/utils/constants';
import { GoogleReCaptchaProvider } from '@google-recaptcha/react';

interface IProps {
    config: GlobalConfig | null;
}

export default function Newsletter({ config }: Readonly<IProps>) {
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors, isSubmitting },
    } = useForm<{ email: string }>({
        resolver: yupResolver(
            yup
                .object({
                    email: yup
                        .string()
                        .required(COMMON_MESSAGE.FIELD_REQUIRED)
                        .trim()
                        .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, COMMON_MESSAGE.FIELD_EMAIL),
                })
                .required()
        ),
    });

    const onSubmit = async (data: { email: string }) => {
        await addSubscribe(data.email);
        reset({ email: '' });
    };

    return (
        <GoogleReCaptchaProvider type="v3" siteKey={GG_RECAPTCHA_KEY}>
            <div className="rbt-newsletter-area newsletter-style-2 bg-color-primary rbt-section-gap">
                <div className="container">
                    <div className="row row--15 align-items-center">
                        <div className="col-lg-12">
                            <div className="inner text-center">
                                <div className="section-title text-center">
                                    <span className="subtitle bg-white-opacity">Giảm 10% các khoá học</span>
                                    <h2 className="title color-white">
                                        <strong>Đăng ký</strong> nhận bản tin
                                    </h2>
                                    <p className="description color-white mt--20">{config?.siteDesc}</p>
                                </div>
                                <form
                                    className="newsletter-form-1 mt--40"
                                    method="POST"
                                    onSubmit={handleSubmit(onSubmit)}
                                >
                                    <input
                                        {...register('email')}
                                        type="email"
                                        className={classNames({ 'border-danger': !!errors.email?.message })}
                                        placeholder="Nhập Email của bạn"
                                    />
                                    <button
                                        type="submit"
                                        disabled={isSubmitting}
                                        className="rbt-btn btn-md btn-gradient hover-icon-reverse"
                                    >
                                        <span className="icon-reverse-wrapper">
                                            <span className="btn-text">Đăng ký</span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right"></i>
                                            </span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right"></i>
                                            </span>
                                        </span>
                                    </button>
                                </form>
                                {/* <span className="note-text color-white mt--20">No ads, No trails, No commitments</span> */}
                                <div className="row row--15 mt--50">
                                    <div className="col-lg-3 offset-lg-3 col-md-6 col-sm-6 single-counter">
                                        <div className="rbt-counterup rbt-hover-03 style-2 text-color-white">
                                            <div className="inner">
                                                <div className="content">
                                                    <h3 className="counter color-white">
                                                        <CounterUp count={100} />
                                                    </h3>
                                                    <h5 className="title color-white">Khoá học Online</h5>
                                                    <span className="subtitle color-white">Học mọi lúc, mọi nơi</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="col-lg-3 col-md-6 col-sm-6 single-counter mt_mobile--30">
                                        <div className="rbt-counterup rbt-hover-03 style-2 text-color-white">
                                            <div className="inner">
                                                <div className="content">
                                                    <h3 className="counter color-white">
                                                        <CounterUp count={100} />
                                                    </h3>
                                                    <h5 className="title color-white">Khoá học Offline</h5>
                                                    <span className="subtitle color-white">Học viện tại Hà Nội</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </GoogleReCaptchaProvider>
    );
}

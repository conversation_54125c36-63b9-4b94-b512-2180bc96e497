'use client';

import { useAuthStore } from '@/stores/authStore';
import { useCartStore } from '@/stores/cartStore';
import Category from '@/types/Category';
import Course from '@/types/Course';
import { GlobalConfig } from '@/types/GlobalConfig';
import { API_URL, ARTICLE_ROUTE, AUTH_KEYS, COURSE_ROUTE, GG_RECAPTCHA_KEY, PRODUCT_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import Cookies from 'js-cookie';
import { startsWith } from 'lodash';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import CartShort from './CartShort';
import CourseMenuItem from './CourseMenuItem';
import CourseMenuSide from './CourseMenuSide';
import LoginForm from './LoginForm';
import MobileMenu from './MobileMenu';
import RegisterForm from './RegisterForm';
import SocialLinks from './SocialLinks';
import TopFormSearch from './TopFormSearch';
import TopPromotion from './TopPromotion';
import UserLinkList from './UserLinkList';
import { GoogleReCaptchaProvider } from '@google-recaptcha/react';
import { logoutLocal } from '@/services/UserService';

interface IProps {
    config: GlobalConfig | null;
    categories: Category[];
    pCategories: Category[];
    highlightCourses: Course[];
}

export default function Header({ config, categories, pCategories, highlightCourses }: Readonly<IProps>) {
    const [showRegister, setShowRegister] = useState(false);
    const [showMenu, setShowMenu] = useState(false);
    const [showCart, setShowCart] = useState(false);
    const user = useAuthStore((state) => state.user);
    const showLogin = useAuthStore((state) => state.showLogin);
    const setShowLogin = useAuthStore((state) => state.setShowLogin);
    const setUser = useAuthStore((state) => state.setUser);
    const cartCourseIds = useCartStore((state) => state.courseIds);
    const cartProducts = useCartStore((state) => state.products);
    const setCourseIds = useCartStore((state) => state.setCourseIds);
    const setCartProducts = useCartStore((state) => state.setProducts);
    const pathname = usePathname();
    const router = useRouter();

    useEffect(() => {
        const u = Cookies.get(AUTH_KEYS.USER_COOKIE);
        if (u) setUser(JSON.parse(u));
        const ids = Cookies.get(AUTH_KEYS.CART);
        if (ids) setCourseIds(JSON.parse(ids));
        const pIds = Cookies.get(AUTH_KEYS.CART_PRODUCT);
        if (pIds) setCartProducts(JSON.parse(pIds));
    }, [setUser, setCourseIds, setCartProducts]);

    useEffect(() => {
        setShowLogin(false);
        setShowRegister(false);
        setShowMenu(false);
        setShowCart(false);
    }, [pathname, setShowLogin]);

    const logout = async () => {
        if (user) {
            await logoutLocal(user.id, user.jwt!);
        }
        setUser(null);
        router.push('/');
    };

    return (
        <GoogleReCaptchaProvider type="v3" siteKey={GG_RECAPTCHA_KEY}>
            <header className="rbt-header rbt-header-9">
                <div className="rbt-sticky-placeholder" />
                <TopPromotion showRegister={!user} setShowRegister={setShowRegister} />
                <div className="rbt-header-middle position-relative rbt-header-mid-1  bg-color-white rbt-border-bottom">
                    <div className="container">
                        <div className="rbt-header-sec align-items-center ">
                            {/* <LanguageCurrency /> */}
                            <TopFormSearch />
                            <div className="rbt-header-sec-col rbt-header-right">
                                <div className="rbt-header-content">
                                    <div className="header-info">
                                        <ul className="quick-access">
                                            <li>
                                                <a
                                                    className="d-none d-xl-block rbt-cart-sidenav-activation cursor-pointer"
                                                    onClick={() => setShowCart(true)}
                                                >
                                                    <i className="feather-shopping-cart" />
                                                    Giỏ hàng
                                                    {cartCourseIds.length + cartProducts.length > 0
                                                        ? ` (${cartCourseIds.length + cartProducts.length})`
                                                        : ''}
                                                </a>
                                                <a
                                                    className="d-block d-xl-none rbt-cart-sidenav-activation cursor-pointer"
                                                    onClick={() => setShowCart(true)}
                                                >
                                                    <i className="feather-shopping-cart" />
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div className="header-info">
                                        <ul className="quick-access">
                                            <li className="account-access rbt-user-wrapper right-align-dropdown d-none d-xl-block">
                                                {user ? (
                                                    <a className="cursor-pointer">
                                                        <i className="feather-user" />
                                                        {user.username}
                                                    </a>
                                                ) : (
                                                    <a className="cursor-pointer" onClick={() => setShowLogin(true)}>
                                                        <i className="feather-user" />
                                                        Đăng nhập
                                                    </a>
                                                )}
                                                <div
                                                    className={classNames('rbt-user-menu-list-wrapper', {
                                                        hidden: !user,
                                                    })}
                                                >
                                                    <div className="inner">
                                                        <ul className="user-list-wrapper">
                                                            <UserLinkList />
                                                        </ul>
                                                        <hr className="mt--10 mb--10" />
                                                        <ul className="user-list-wrapper">
                                                            <li>
                                                                <a className="cursor-pointer" onClick={logout}>
                                                                    <i className="feather-log-out" />
                                                                    <span>Đăng xuất</span>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </li>
                                            <li className="access-icon rbt-user-wrapper right-align-dropdown d-block d-xl-none">
                                                {/* <a className="rbt-round-btn" onClick={() => setShowLogin(true)}>
                                                    <i className="feather-user" />
                                                </a> */}
                                                {user ? (
                                                    <a className="rbt-round-btn cursor-pointer">
                                                        <i className="feather-user" />
                                                    </a>
                                                ) : (
                                                    <a
                                                        className="rbt-round-btn cursor-pointer"
                                                        onClick={() => setShowLogin(true)}
                                                    >
                                                        <i className="feather-user" />
                                                    </a>
                                                )}
                                                <div
                                                    className={classNames('rbt-user-menu-list-wrapper', {
                                                        hidden: !user,
                                                    })}
                                                >
                                                    <div className="inner">
                                                        <ul className="user-list-wrapper">
                                                            <UserLinkList />
                                                        </ul>
                                                        <hr className="mt--10 mb--10" />
                                                        <ul className="user-list-wrapper">
                                                            <li>
                                                                <a className="cursor-pointer" onClick={logout}>
                                                                    <i className="feather-log-out" />
                                                                    <span>Đăng xuất</span>
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="rbt-header-wrapper  header-not-transparent header-sticky">
                    <div className="container">
                        <div className="mainbar-row rbt-navigation-end align-items-center">
                            <div className="header-left rbt-header-content">
                                <div className="header-info">
                                    <div className="logo">
                                        <Link href="/">
                                            <img src={`${API_URL}${config?.logo.url}`} alt={config?.siteName} />
                                        </Link>
                                    </div>
                                </div>
                            </div>
                            <div className="rbt-main-navigation d-none d-xl-block">
                                <nav className="mainmenu-nav">
                                    <ul className="mainmenu" id="mainmenu">
                                        <li>
                                            <Link href="/" className={classNames({ active: pathname === '/' })}>
                                                Trang chủ
                                            </Link>
                                        </li>
                                        <li className="with-megamenu has-menu-child-item position-static">
                                            <Link href="/khoa-hoc-noi-bat">
                                                Nổi bật <i className="feather-chevron-down" />
                                            </Link>
                                            <div className="rbt-megamenu menu-skin-dark">
                                                <div className="wrapper">
                                                    <div className="row row--15 home-plesentation-wrapper single-dropdown-menu-presentation">
                                                        {highlightCourses.slice(0, 12).map((item) => (
                                                            <CourseMenuItem key={item.id} item={item} />
                                                        ))}{' '}
                                                    </div>
                                                    <div className="load-demo-btn text-center">
                                                        <a className="rbt-btn-link color-white" href="#">
                                                            Scroll to view more{' '}
                                                            <svg
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                width={16}
                                                                height={16}
                                                                fill="currentColor"
                                                                className="bi bi-arrow-down-up"
                                                                viewBox="0 0 16 16"
                                                            >
                                                                <path
                                                                    fillRule="evenodd"
                                                                    d="M11.5 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L11 2.707V14.5a.5.5 0 0 0 .5.5zm-7-14a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L4 13.293V1.5a.5.5 0 0 1 .5-.5z"
                                                                />
                                                            </svg>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li className="with-megamenu has-menu-child-item">
                                            <Link
                                                href={COURSE_ROUTE}
                                                className={classNames({
                                                    active: startsWith(pathname ?? '', COURSE_ROUTE),
                                                })}
                                            >
                                                Khoá học <i className="feather-chevron-down" />
                                            </Link>
                                            <div className="rbt-megamenu grid-item-2">
                                                <div className="wrapper">
                                                    <div className="row">
                                                        <div className="col-lg-12">
                                                            <div className="mega-top-banner">
                                                                <div className="content">
                                                                    <h4 className="title">{config?.siteName}</h4>
                                                                    <p className="description">{config?.siteDesc}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="row row--15">
                                                        <CourseMenuSide
                                                            isOffline={false}
                                                            items={highlightCourses
                                                                .filter((item) => !item.is_offline)
                                                                .slice(0, 7)}
                                                        />
                                                        <CourseMenuSide
                                                            isOffline={true}
                                                            items={highlightCourses
                                                                .filter((item) => item.is_offline)
                                                                .slice(0, 7)}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li className="has-dropdown has-menu-child-item">
                                            <Link href="/the-loai">
                                                Thể loại
                                                <i className="feather-chevron-down" />
                                            </Link>
                                            <ul className="submenu">
                                                {categories.map((item) => (
                                                    <li key={item.id}>
                                                        <Link href={`/the-loai/${item.slug}-${item.id}`}>
                                                            {item.title}
                                                        </Link>
                                                    </li>
                                                ))}
                                            </ul>
                                        </li>
                                        <li className="has-dropdown has-menu-child-item">
                                            <Link href={PRODUCT_ROUTE}>
                                                Sản phẩm
                                                <i className="feather-chevron-down" />
                                            </Link>
                                            <ul className="submenu">
                                                {pCategories.map((item) => (
                                                    <li key={item.id}>
                                                        <Link href={`/chuyen-muc/${item.slug}-${item.id}`}>
                                                            {item.title}
                                                        </Link>
                                                    </li>
                                                ))}
                                            </ul>
                                        </li>
                                        <li>
                                            <Link
                                                href={ARTICLE_ROUTE}
                                                className={classNames({
                                                    active: startsWith(pathname ?? '', ARTICLE_ROUTE),
                                                })}
                                            >
                                                Tin tức
                                            </Link>
                                        </li>
                                        <li>
                                            <Link
                                                href="/lien-he"
                                                className={classNames({ active: pathname === '/lien-he' })}
                                            >
                                                Liên hệ
                                            </Link>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                            <div className="header-right">
                                {!user && (
                                    <div className="rbt-btn-wrapper d-none d-xl-block">
                                        <a
                                            className="rbt-btn rbt-switch-btn btn-gradient btn-sm hover-transform-none cursor-pointer"
                                            onClick={() => setShowRegister(true)}
                                        >
                                            <span data-text="Đăng ký ngay">Đăng ký ngay</span>
                                        </a>
                                    </div>
                                )}
                                <div className="mobile-menu-bar d-block d-xl-none">
                                    <div className="hamberger">
                                        <button
                                            className="hamberger-button rbt-round-btn"
                                            onClick={() => setShowMenu(true)}
                                        >
                                            <i className="feather-menu" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            <div className={classNames('popup-mobile-menu', { active: showMenu })}>
                <div className="inner-wrapper">
                    <div className="inner-top">
                        <div className="content">
                            <div className="logo">
                                <Link href="/">
                                    <img src={`${API_URL}${config?.logo.url}`} alt={config?.siteName} />
                                </Link>
                            </div>
                            <div className="rbt-btn-close">
                                <button className="close-button rbt-round-btn" onClick={() => setShowMenu(false)}>
                                    <i className="feather-x" />
                                </button>
                            </div>
                        </div>
                        <p className="description">{config?.siteDesc}</p>
                        <ul className="navbar-top-left rbt-information-list justify-content-start">
                            <li>
                                <a href={`mailto:${config?.email}`}>
                                    <i className="feather-mail" />
                                    {config?.email}
                                </a>
                            </li>
                            <li>
                                <a href={`tel:${config?.phone}`}>
                                    <i className="feather-phone" />
                                    {config?.phone}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <MobileMenu
                        categories={categories ?? []}
                        pCategories={pCategories ?? []}
                        highlightCourses={highlightCourses}
                        pathname={pathname ?? ''}
                    />
                    <div className="mobile-menu-bottom">
                        {!user && (
                            <div className="rbt-btn-wrapper mb--20">
                                <a
                                    className="rbt-btn btn-border-gradient radius-round btn-sm hover-transform-none w-100 justify-content-center text-center cursor-pointer"
                                    onClick={() => setShowRegister(true)}
                                >
                                    <span>Đăng ký ngay</span>
                                </a>
                            </div>
                        )}
                        <div className="social-share-wrapper">
                            <span className="rbt-short-title d-block">Tham gia cộng đồng</span>
                            <SocialLinks className="transparent-with-border justify-content-start mt--20" />
                        </div>
                    </div>
                </div>
            </div>
            <CartShort
                show={showCart}
                cartCourseIds={cartCourseIds}
                cartProducts={cartProducts}
                setShow={setShowCart}
            />
            <LoginForm show={showLogin} setShow={setShowLogin} setShowRegister={setShowRegister} />
            <RegisterForm show={showRegister} setShow={setShowRegister} setShowLogin={setShowLogin} />
        </GoogleReCaptchaProvider>
    );
}

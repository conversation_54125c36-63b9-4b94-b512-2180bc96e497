'use client';

import { useCartStore } from '@/stores/cartStore';

interface IProps {
    productId: string;
}

export default function AddCartProduct({ productId }: Readonly<IProps>) {
    const cartProducts = useCartStore((state) => state.products);
    const setCartProducts = useCartStore((state) => state.setProducts);

    return !cartProducts.find((item) => item.label === productId) ? (
        <li>
            <a
                className="cursor-pointer"
                title="Thêm vào giỏ"
                onClick={() =>
                    setCartProducts([
                        ...cartProducts,
                        {
                            label: productId,
                            value: 1,
                        },
                    ])
                }
            >
                <i className="feather-shopping-cart" />
            </a>
        </li>
    ) : (
        <></>
    );
}

'use client';

import classNames from 'classnames';
import React from 'react';

interface IProps {
    text: string;
    className: string[];
}

export default function ItemText({ text, className }: Readonly<IProps>) {
    const onCopy = (e: React.ClipboardEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
    };

    return (
        <div
            className={classNames('text-content', className)}
            onCopy={onCopy}
            dangerouslySetInnerHTML={{ __html: text }}
        />
    );
}

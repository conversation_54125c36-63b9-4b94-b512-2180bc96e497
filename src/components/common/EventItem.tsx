import Course from '@/types/Course';
import { API_URL, COURSE_ROUTE } from '@/utils/constants';
import Link from 'next/link';
import CoursePrice from './CoursePrice';

interface IProps {
    item: Course;
}

export default function EventItem({ item }: Readonly<IProps>) {
    return (
        <div className="swiper-slide">
            <div className="single-slide">
                <div className="rbt-card event-grid-card variation-01 rbt-hover">
                    <div className="rbt-card-img">
                        <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                            <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                            <div className="rbt-badge-3 bg-white">
                                <span>01/04</span>
                                <span>2024</span>
                            </div>
                        </Link>
                    </div>
                    <div className="rbt-card-body">
                        <ul className="rbt-meta">
                            <li>
                                <i className="feather-map-pin"></i>H<PERSON>i
                            </li>
                            <li>
                                <i className="feather-clock"></i>3 ngày/ tuần
                            </li>
                        </ul>
                        <h4 className="rbt-card-title">
                            <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>{item.title}</Link>
                        </h4>
                        <div className="rbt-card-bottom mb--20">
                            <CoursePrice price={item.price} salePrice={item.sale_price} />
                        </div>
                        <div className="read-more-btn">
                            <Link
                                className="rbt-btn btn-border hover-icon-reverse btn-sm radius-round"
                                href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}
                            >
                                <span className="icon-reverse-wrapper">
                                    <span className="btn-text">Đăng ký tham gia</span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right"></i>
                                    </span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right"></i>
                                    </span>
                                </span>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

'use client';

import CounterUp from './CounterUp';

interface IProps {
    color: 'primary' | 'secondary' | 'violet';
    icon: string;
    count: number;
    text: string;
}

export default function DashboardItem({ color, icon, count, text }: Readonly<IProps>) {
    return (
        <div className="col-lg-4 col-md-4 col-sm-6 col-12">
            <div className={`rbt-counterup variation-01 rbt-hover-03 rbt-border-dashed bg-${color}-opacity`}>
                <div className="inner">
                    <div className={`rbt-round-icon bg-${color}-opacity`}>
                        <i className={icon} />
                    </div>
                    <div className="content">
                        <h3 className={`counter without-icon color-${color}`}>
                            <CounterUp count={count} />
                        </h3>
                        <span className="rbt-title-style-2 d-block">{text}</span>
                    </div>
                </div>
            </div>
        </div>
    );
}

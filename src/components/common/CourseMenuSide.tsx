import Course from '@/types/Course';
import { COURSE_ROUTE } from '@/utils/constants';
import Link from 'next/link';

interface IProps {
    isOffline: boolean;
    items: Course[];
}

export default function CourseMenuSide({ isOffline, items }: Readonly<IProps>) {
    return (
        <div className="col-lg-12 col-xl-6 col-xxl-6 single-mega-item">
            <h3 className="rbt-short-title">
                <Link href={isOffline ? '/khoa-hoc-tai-trung-tam' : '/khoa-hoc-online-truc-tuyen'}>
                    {isOffline ? 'Tại trung tâm' : 'Online trực tuyến'}
                </Link>
            </h3>
            <ul className="mega-menu-item">
                {items.map((item) => (
                    <li key={item.id}>
                        <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>{item.title}</Link>
                    </li>
                ))}
            </ul>
        </div>
    );
}

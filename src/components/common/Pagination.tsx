'use client';

import { BaseSearch, PaginationData } from '@/types/common';
import { SearchCourse } from '@/types/Course';
import { getSearchCourseRoute, getSearchRoute } from '@/utils';
import { COURSE_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { useRouter } from 'next/navigation';

interface IProps {
    baseUrl: string;
    searchParams: BaseSearch | SearchCourse;
    pagination: PaginationData;
}

export default function Pagination({ baseUrl, searchParams, pagination }: Readonly<IProps>) {
    const router = useRouter();

    const getPageNumbers = () => {
        const pages = [];
        const maxPageNumbersToShow = 5;
        const halfMax = Math.floor(maxPageNumbersToShow / 2);
        pages.push(1);
        if (pagination.page > halfMax + 2) {
            pages.push('...');
        }
        for (
            let i = Math.max(2, pagination.page - halfMax);
            i <= Math.min(pagination.pageCount - 1, pagination.page + halfMax);
            i++
        ) {
            pages.push(i);
        }
        if (pagination.page < pagination.pageCount - halfMax - 1) {
            pages.push('...');
        }
        if (pagination.pageCount > 1) {
            pages.push(pagination.pageCount);
        }
        return pages;
    };

    const handlePageChange = (page: number) => {
        const params = { ...searchParams, page };
        const url = baseUrl === COURSE_ROUTE ? getSearchCourseRoute(params) : getSearchRoute(baseUrl, params);
        router.push(url);
    };

    return pagination.pageCount > 1 ? (
        <div className="row">
            <div className="col-lg-12 mt--60">
                <nav>
                    <ul className="rbt-pagination">
                        {pagination.page > 1 && (
                            <li>
                                <a className="cursor-pointer" onClick={() => handlePageChange(pagination.page - 1)}>
                                    <i className="feather-chevron-left" />
                                </a>
                            </li>
                        )}
                        {getPageNumbers().map((page, index) =>
                            typeof page === 'number' ? (
                                <li key={page} className={classNames({ active: page === pagination.page })}>
                                    <a className="cursor-pointer" onClick={() => handlePageChange(page)}>
                                        {page}
                                    </a>
                                </li>
                            ) : (
                                <li key={`ellipsis-${index}`}>
                                    <span>...</span>
                                </li>
                            )
                        )}
                        {pagination.page < pagination.pageCount && (
                            <li>
                                <a className="cursor-pointer" onClick={() => handlePageChange(pagination.page + 1)}>
                                    <i className="feather-chevron-right" />
                                </a>
                            </li>
                        )}
                    </ul>
                </nav>
            </div>
        </div>
    ) : (
        <></>
    );
}

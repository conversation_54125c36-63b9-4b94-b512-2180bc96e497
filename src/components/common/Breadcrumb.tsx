import { ItemLink } from '@/types/common';
import classNames from 'classnames';
import Link from 'next/link';
import { Fragment } from 'react';

interface IProps {
    items: ItemLink[];
}

export default function Breadcrumb({ items }: Readonly<IProps>) {
    return (
        <ul className="page-list">
            {items.map((item) => (
                <Fragment key={item.id}>
                    <li className={classNames('rbt-breadcrumb-item', { active: item.id === 0 })}>
                        {item.id > 0 ? <Link href={item.link}>{item.name}</Link> : item.name}
                    </li>
                    {item.id > 0 && (
                        <li>
                            <div className="icon-right">
                                <i className="feather-chevron-right" />
                            </div>
                        </li>
                    )}
                </Fragment>
            ))}
        </ul>
    );
}

'use client';

import { getCourses } from '@/services/CourseService';
import { useCartStore } from '@/stores/cartStore';
import Course from '@/types/Course';
import { formatNumber, showToast } from '@/utils';
import { API_URL, COMMON_MESSAGE, COURSE_ROUTE, PRODUCT_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { find, isEmpty, sumBy, uniq } from 'lodash';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { LabelValue } from '@/types/common';
import Product from '@/types/Product';
import { getProducts } from '@/services/ProductService';

interface IProps {
    show: boolean;
    cartCourseIds: string[];
    cartProducts: LabelValue[];
    setShow: (s: boolean) => void;
}
export default function CartShort({ show, cartCourseIds, cartProducts, setShow }: Readonly<IProps>) {
    const [courseIds, setCourseIds] = useState<string[]>([]);
    const [cloneCartProducts, setCloneCartProducts] = useState<LabelValue[]>([]);
    const [courses, setCourses] = useState<Course[]>([]);
    const [products, setProducts] = useState<Product[]>([]);
    const setCartCourseIds = useCartStore((state) => state.setCourseIds);
    const setCartProducts = useCartStore((state) => state.setProducts);

    useEffect(() => {
        const getCartCourses = async (documentIds: string[]) => {
            const response = await getCourses({ documentIds });
            setCourses(response.data ?? []);
        };

        const getCartProducts = async (documentIds: string[]) => {
            const response = await getProducts({ documentIds });
            setProducts(response.data ?? []);
        };

        setCourseIds(cartCourseIds);
        setCloneCartProducts(cartProducts);
        if (show) {
            if (!isEmpty(cartCourseIds)) getCartCourses(uniq(cartCourseIds));
            if (!isEmpty(cartProducts)) getCartProducts(uniq(cartProducts.map((item) => item.label)));
        }
    }, [show, cartCourseIds, cartProducts]);

    const removeCart = (id: string, isCourse: boolean) => {
        if (isCourse && courseIds.includes(id)) {
            const items = [...courseIds].filter((item) => item !== id);
            setCourseIds(items);
            setCartCourseIds(items);
            setCourses([...courses].filter((item) => item.documentId !== id));
            showToast(true, COMMON_MESSAGE.DELETE_CART);
        }
        if (!isCourse && cloneCartProducts.map((item) => item.label).includes(id)) {
            const items = [...cloneCartProducts].filter((item) => item.label !== id);
            setCloneCartProducts(items);
            setCartProducts(items);
            setProducts([...products].filter((item) => item.documentId !== id));
            showToast(true, COMMON_MESSAGE.DELETE_CART);
        }
    };

    return (
        <div className={classNames('rbt-cart-side-menu', { 'side-menu-active': show })}>
            <div className="inner-wrapper">
                <div className="inner-top">
                    <div className="content">
                        <div className="title">
                            <h4 className="title mb--0">Giỏ hàng</h4>
                        </div>
                        <div className="rbt-btn-close">
                            <button className="minicart-close-button rbt-round-btn" onClick={() => setShow(false)}>
                                <i className="feather-x" />
                            </button>
                        </div>
                    </div>
                </div>
                <nav className="side-nav w-100">
                    <ul className="rbt-minicart-wrapper">
                        {courses.map((item) => (
                            <li key={item.id} className="minicart-item">
                                <div className="thumbnail">
                                    <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                                        <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                                    </Link>
                                </div>
                                <div className="product-content">
                                    <span className="rbt-badge-5 bg-color-primary-opacity color-primary">Khoá học</span>
                                    <h6 className="title">
                                        <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                                            {item.title}
                                        </Link>
                                    </h6>
                                    <span className="quantity">
                                        <span className="price theme-gradient">{formatNumber(item.sale_price)}đ</span>
                                    </span>
                                </div>
                                <div className="close-btn">
                                    <button className="rbt-round-btn" onClick={() => removeCart(item.documentId, true)}>
                                        <i className="feather-x" />
                                    </button>
                                </div>
                            </li>
                        ))}
                        {products.map((item) => (
                            <li key={item.id} className="minicart-item">
                                <div className="thumbnail">
                                    <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>
                                        <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                                    </Link>
                                </div>
                                <div className="product-content">
                                    <span className="rbt-badge-5 bg-color-success-opacity color-success">Sản phẩm</span>
                                    <h6 className="title">
                                        <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>
                                            {item.title}
                                        </Link>
                                    </h6>
                                    <span className="quantity">
                                        <span className="price theme-gradient">
                                            {find(cloneCartProducts, (cp) => cp.label === item.documentId)?.value ?? 1}{' '}
                                            x {formatNumber(item.sale_price)}đ
                                        </span>
                                    </span>
                                </div>
                                <div className="close-btn">
                                    <button
                                        className="rbt-round-btn"
                                        onClick={() => removeCart(item.documentId, false)}
                                    >
                                        <i className="feather-x" />
                                    </button>
                                </div>
                            </li>
                        ))}
                    </ul>
                </nav>
                <div className="rbt-minicart-footer">
                    <hr className="mb--0" />
                    <div className="rbt-cart-subttotal">
                        <p className="subtotal">
                            <strong>Tổng tiền:</strong>
                        </p>
                        <p className="price theme-gradient">
                            {formatNumber(
                                sumBy(courses, (item) => item.sale_price) +
                                    sumBy(
                                        products,
                                        (item) =>
                                            item.sale_price *
                                            (find(cloneCartProducts, (cp) => cp.label === item.documentId)?.value ?? 1)
                                    )
                            )}
                            đ
                        </p>
                    </div>
                    <hr className="mb--0" />
                    <div className="rbt-minicart-bottom mt--20">
                        {/* <div className="view-cart-btn">
                            <Link className="rbt-btn btn-border icon-hover w-100 text-center" href="/gio-hang">
                                <span className="btn-text">Xem chi tiết</span>
                                <span className="btn-icon">
                                    <i className="feather-arrow-right" />
                                </span>
                            </Link>
                        </div> */}
                        {!isEmpty(courses) && (
                            <div className="checkout-btn mt--20">
                                <Link className="rbt-btn btn-gradient icon-hover w-100 text-center" href="/thanh-toan">
                                    <span className="btn-text">Thanh toán</span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right" />
                                    </span>
                                </Link>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

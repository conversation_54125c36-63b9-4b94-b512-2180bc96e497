'use client';

import { addBookmarkCourse, deleteBookmarkCourse } from '@/services/BookmarkCourseService';
import BookmarkCourse from '@/types/BookmarkCourse';
import { showToast } from '@/utils';
import classNames from 'classnames';
import { find } from 'lodash';
import { useEffect, useState } from 'react';
import { COMMON_MESSAGE } from '@/utils/constants';

interface IProps {
    id: number;
    userId: number;
    bookmarks: BookmarkCourse[];
}

export default function BookmarkAction({ id, userId, bookmarks }: Readonly<IProps>) {
    const [items, setItems] = useState<BookmarkCourse[]>([]);

    useEffect(() => setItems(bookmarks), [bookmarks]);

    const onBookmark = async () => {
        if (userId && userId > 0) {
            if (items.map((b) => b.course_id).includes(id)) {
                const response = await deleteBookmarkCourse(find(items, { course_id: id })?.documentId ?? '');
                if (response) {
                    setItems([...items].filter((item) => item.course_id !== id));
                } else showToast(false, COMMON_MESSAGE.ERROR_MESSAGE);
            } else {
                const response = await addBookmarkCourse({ user_id: userId, course_id: id });
                if (response.data) {
                    setItems([...items, response.data]);
                } else showToast(false, COMMON_MESSAGE.ERROR_MESSAGE);
            }
        } else showToast(false, 'Vui lòng đăng nhập để sử dụng chức năng này');
    };

    return (
        <div className="rbt-bookmark-btn">
            <a
                className={classNames('rbt-round-btn cursor-pointer', {
                    open: items.map((b) => b.course_id).includes(id),
                })}
                title="Bookmark"
                onClick={onBookmark}
            >
                <i className="feather-bookmark"></i>
            </a>
        </div>
    );
}

import BookmarkCourse from '@/types/BookmarkCourse';
import Course from '@/types/Course';
import { GlobalConfig } from '@/types/GlobalConfig';
import { API_URL, COURSE_ROUTE } from '@/utils/constants';
import Link from 'next/link';
import BookmarkAction from './BookmarkAction';
import CourseAuthor from './CourseAuthor';
import CoursePrice from './CoursePrice';

interface IProps {
    item: Course;
    userId: number;
    bookmarks: BookmarkCourse[];
    config: GlobalConfig | null;
    isPending?: boolean;
}

export default function CourseItem({ item, userId, bookmarks, config, isPending }: Readonly<IProps>) {
    return (
        <div className="rbt-card variation-01 rbt-hover">
            <div className="rbt-card-img">
                <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                    <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                    <div className="rbt-badge-3 bg-white">
                        <span>-50%</span>
                        <span>Off</span>
                    </div>
                </Link>
            </div>
            <div className="rbt-card-body">
                <div className="rbt-card-top">
                    <div className="rbt-review">
                        <div className="rating">
                            <i className="fas fa-star"></i>
                            <i className="fas fa-star"></i>
                            <i className="fas fa-star"></i>
                            <i className="fas fa-star"></i>
                            <i className="fas fa-star"></i>
                        </div>
                        <span className="rating-count mr--5">(15 Reviews)</span>
                        {isPending && <span className="rating-count color-danger">Chờ xác nhận</span>}
                    </div>
                    <BookmarkAction id={item.id} userId={userId} bookmarks={bookmarks} />
                </div>
                <h4 className="rbt-card-title">
                    <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>{item.title}</Link>
                </h4>
                <ul className="rbt-meta mb--5">
                    <li>
                        <i className="feather-book"></i>20 bài học
                    </li>
                    <li>
                        <i className="feather-clock"></i>40 phút
                    </li>
                </ul>
                <div className="rbt-card-text">{item.description}</div>
                <CourseAuthor config={config} />
                <div className="rbt-card-bottom">
                    <CoursePrice price={item.price} salePrice={item.sale_price} />
                    {/* <a className="rbt-btn-link" href="#">Xem<i className="feather-arrow-right"></i></a> */}
                </div>
            </div>
        </div>
    );
}

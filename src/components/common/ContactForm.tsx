'use client';

import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import classNames from 'classnames';
import { COMMON_MESSAGE, GG_RECAPTCHA_KEY } from '@/utils/constants';
import { showToast } from '@/utils';
import { GoogleReCaptchaProvider } from '@google-recaptcha/react';

interface IContact {
    name: string;
    email: string;
    subject: string;
    message: string;
}

export default function ContactForm() {
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors, isSubmitting },
    } = useForm<IContact>({
        resolver: yupResolver(
            yup
                .object({
                    name: yup.string().required(COMMON_MESSAGE.FIELD_REQUIRED).trim(),
                    email: yup
                        .string()
                        .required(COMMON_MESSAGE.FIELD_REQUIRED)
                        .trim()
                        .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, COMMON_MESSAGE.FIELD_EMAIL),
                    subject: yup.string().required(COMMON_MESSAGE.FIELD_REQUIRED).trim(),
                    message: yup.string().required(COMMON_MESSAGE.FIELD_REQUIRED).trim(),
                })
                .required()
        ),
    });

    const onSubmit = async (data: IContact) => {
        try {
            const response = await fetch('/api/send-email', {
                method: 'POST',
                next: { revalidate: 60 },
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });
            const result = await response.json();
            if (response.ok) {
                showToast(true, result.message);
                reset({ name: '', email: '', subject: '', message: '' });
            } else showToast(false, result.message ?? COMMON_MESSAGE.ERROR_MESSAGE);
        } catch {
            showToast(false, COMMON_MESSAGE.ERROR_MESSAGE);
        }
    };

    return (
        <GoogleReCaptchaProvider type="v3" siteKey={GG_RECAPTCHA_KEY}>
            <div className="rbt-contact-address">
                <div className="container">
                    <div className="row g-5">
                        <div className="col-lg-6">
                            <div className="thumbnail">
                                <img className="w-100 radius-6" src="/assets/images/about/contact.jpg" alt="" />
                            </div>
                        </div>
                        <div className="col-lg-6">
                            <div className="rbt-contact-form contact-form-style-1 max-width-auto">
                                <div className="section-title text-start">
                                    <span className="subtitle bg-primary-opacity">Học viện Yêu B & G</span>
                                </div>
                                <h3 className="title">Liên hệ với chúng tôi</h3>
                                <form
                                    onSubmit={handleSubmit(onSubmit)}
                                    method="POST"
                                    className="rainbow-dynamic-form max-width-auto"
                                >
                                    <div className="form-group focused">
                                        <label className={classNames({ 'color-danger': !!errors.name?.message })}>
                                            Họ và tên
                                        </label>
                                        <input {...register('name')} type="text" />
                                        <span className="focus-border" />
                                        <label className="color-danger text-right">{errors.name?.message}</label>
                                    </div>
                                    <div className="form-group focused">
                                        <label className={classNames({ 'color-danger': !!errors.email?.message })}>
                                            Email
                                        </label>
                                        <input {...register('email')} type="email" />
                                        <span className="focus-border" />
                                        <label className="color-danger text-right">{errors.email?.message}</label>
                                    </div>
                                    <div className="form-group focused">
                                        <label className={classNames({ 'color-danger': !!errors.subject?.message })}>
                                            Tiêu đề
                                        </label>
                                        <input {...register('subject')} type="text" />
                                        <span className="focus-border" />
                                        <label className="color-danger text-right">{errors.subject?.message}</label>
                                    </div>
                                    <div className="form-group focused">
                                        <label className={classNames({ 'color-danger': !!errors.message?.message })}>
                                            Nội dung
                                        </label>
                                        <textarea {...register('message')} />
                                        <span className="focus-border" />
                                        <label className="color-danger text-right">{errors.message?.message}</label>
                                    </div>
                                    <div className="form-submit-group">
                                        <button
                                            type="submit"
                                            disabled={isSubmitting}
                                            className="rbt-btn btn-md btn-gradient hover-icon-reverse w-100"
                                        >
                                            <span className="icon-reverse-wrapper">
                                                <span className="btn-text">GỬI</span>
                                                <span className="btn-icon">
                                                    <i className="feather-arrow-right" />
                                                </span>
                                                <span className="btn-icon">
                                                    <i className="feather-arrow-right" />
                                                </span>
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </GoogleReCaptchaProvider>
    );
}

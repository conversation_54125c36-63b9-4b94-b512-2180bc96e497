import { GlobalConfig } from '@/types/GlobalConfig';
import { API_URL, COURSE_ROUTE } from '@/utils/constants';
import Link from 'next/link';

interface IProps {
    config: GlobalConfig | null;
}

export default function CourseAuthor({ config }: Readonly<IProps>) {
    return (
        <div className="rbt-author-meta mb--10">
            <div className="rbt-avater">
                <Link href={COURSE_ROUTE}>
                    <img src={`${API_URL}${config?.avatar.url}`} alt={config?.siteName} />
                </Link>
            </div>
            <div className="rbt-author-info">{config?.siteName}</div>
        </div>
    );
}

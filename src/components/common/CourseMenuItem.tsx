import Course from '@/types/Course';
import { API_URL, COURSE_ROUTE } from '@/utils/constants';
import Link from 'next/link';

interface IProps {
    item: Course;
}

export default function CourseMenuItem({ item }: Readonly<IProps>) {
    return (
        <div className="col-lg-12 col-xl-2 col-xxl-2 col-md-12 col-sm-12 col-12 single-mega-item">
            <div className="demo-single">
                <div className="inner">
                    <div className="thumbnail">
                        <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                            <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                        </Link>
                    </div>
                    <div className="content">
                        <h4 className="title">
                            <Link href={`${COURSE_ROUTE}/${item.slug}-${item.documentId}`}>
                                {item.title}
                                <span className="btn-icon">
                                    <i className="feather-arrow-right" />
                                </span>
                            </Link>
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    );
}

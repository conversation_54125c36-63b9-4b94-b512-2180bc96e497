import { formatNumber } from '@/utils';
import classNames from 'classnames';

interface IProps {
    price: number;
    salePrice: number;
    wrapperClass?: string;
}

export default function CoursePrice({ price, salePrice, wrapperClass }: Readonly<IProps>) {
    return (
        <div className={classNames('rbt-price', wrapperClass)}>
            <span className="current-price theme-gradient">{formatNumber(salePrice)}đ</span>
            {price > 0 && <span className="off-price">{formatNumber(price)}đ</span>}
        </div>
    );
}

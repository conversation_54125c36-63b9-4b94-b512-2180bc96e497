'use client';
import { registerUser } from '@/services/UserService';
import { useAuthStore } from '@/stores/authStore';
import User from '@/types/User';
import { showToast } from '@/utils';
import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { COMMON_MESSAGE } from '@/utils/constants';

interface IProps {
    show: boolean;
    setShow: (s: boolean) => void;
    setShowLogin: (s: boolean) => void;
}

export default function RegisterForm({ show, setShow, setShowLogin }: Readonly<IProps>) {
    const setUser = useAuthStore((state) => state.setUser);

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors, isSubmitting },
    } = useForm<User>({
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        resolver: yupResolver(
            yup
                .object({
                    username: yup.string().required(COMMON_MESSAGE.FIELD_REQUIRED).trim(),
                    email: yup
                        .string()
                        .required(COMMON_MESSAGE.FIELD_REQUIRED)
                        .trim()
                        .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, COMMON_MESSAGE.FIELD_EMAIL),
                    password: yup
                        .string()
                        .required(COMMON_MESSAGE.FIELD_REQUIRED)
                        .trim()
                        .min(6, COMMON_MESSAGE.FIELD_PASSWORD_LENGTH),
                    rePassword: yup
                        .string()
                        .required(COMMON_MESSAGE.FIELD_REQUIRED)
                        .trim()
                        .min(6, COMMON_MESSAGE.FIELD_PASSWORD_LENGTH)
                        .oneOf([yup.ref('password'), ''], COMMON_MESSAGE.FIELD_PASSWORD_MATCH),
                })
                .required()
        ),
    });

    const onSubmit = async (data: User) => {
        const response = await registerUser(data);
        if (response.user) {
            setUser({ ...response.user, jwt: response.jwt });
            reset({ username: '', email: '', password: '' });
            showToast(true, 'Đăng ký thành công');
            setShow(false);
        } else showToast(false, COMMON_MESSAGE.ERROR_MESSAGE);
    };

    return (
        <div className={classNames('rbt-cart-side-menu', { 'side-menu-active': show })}>
            <div className="inner-wrapper" style={{ display: 'block' }}>
                <div className="inner-top">
                    <div className="content">
                        <div className="title">
                            <h4 className="title mb--0">Đăng ký</h4>
                        </div>
                        <div className="rbt-btn-close">
                            <button className="minicart-close-button rbt-round-btn" onClick={() => setShow(false)}>
                                <i className="feather-x" />
                            </button>
                        </div>
                    </div>
                </div>
                <div className="side-nav w-100">
                    <form className="w-90p" method="POST" onSubmit={handleSubmit(onSubmit)}>
                        <div className="form-group focused">
                            <label className={classNames({ 'color-danger': !!errors.username?.message })}>
                                Họ và tên
                            </label>
                            <input {...register('username')} type="text" />
                            <span className="focus-border" />
                            <label className="color-danger text-right">{errors.username?.message}</label>
                        </div>
                        <div className="form-group focused">
                            <label className={classNames({ 'color-danger': !!errors.email?.message })}>Email</label>
                            <input {...register('email')} type="email" />
                            <span className="focus-border" />
                            <label className="color-danger text-right">{errors.email?.message}</label>
                        </div>
                        <div className="form-group focused">
                            <label className={classNames({ 'color-danger': !!errors.password?.message })}>
                                Mật khẩu
                            </label>
                            <input {...register('password')} type="password" />
                            <span className="focus-border" />
                            <label className="color-danger text-right">{errors.password?.message}</label>
                        </div>
                        <div className="form-group focused">
                            <label className={classNames({ 'color-danger': !!errors.rePassword?.message })}>
                                Nhập lại mật khẩu
                            </label>
                            <input {...register('rePassword')} type="password" />
                            <span className="focus-border" />
                            <label className="color-danger text-right">{errors.rePassword?.message}</label>
                        </div>
                        <div className="form-submit-group">
                            <button
                                type="submit"
                                disabled={isSubmitting}
                                className="rbt-btn btn-md btn-gradient hover-icon-reverse w-100"
                            >
                                <span className="icon-reverse-wrapper">
                                    <span className="btn-text">Đăng ký</span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right" />
                                    </span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right" />
                                    </span>
                                </span>
                            </button>
                        </div>
                        <div className="rbt-lost-password mb--30">
                            <a
                                className="rbt-btn-link w-400 cursor-pointer"
                                onClick={() => {
                                    setShow(false);
                                    setShowLogin(true);
                                }}
                            >
                                Đã có tài khoản, Đăng nhập ngay
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}

'use client';

import classNames from 'classnames';
import { useEffect, useState } from 'react';

export default function BackToTop() {
    const [show, setShow] = useState(false);

    const onScroll = () => setShow(window.pageYOffset > 50);

    useEffect(() => {
        window.addEventListener('scroll', onScroll);
        return () => window.removeEventListener('scroll', onScroll);
    }, []);

    return (
        <div
            className={classNames('rbt-progress-parent', { 'rbt-backto-top-active': show })}
            onClick={() => window.scrollTo({ left: 0, top: 0, behavior: 'smooth' })}
        >
            <svg className="rbt-back-circle svg-inner" width="100%" height="100%" viewBox="-1 -1 102 102">
                <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" />
            </svg>
        </div>
    );
}

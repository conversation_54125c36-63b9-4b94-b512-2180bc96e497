interface IProps {
    title: string;
    description: string;
    countText?: string;
}

export default function PageTitle({ title, description, countText }: Readonly<IProps>) {
    return (
        <>
            <div className="title-wrapper">
                <h1 className="title mb--0">{title}</h1>
                {countText && (
                    <span className="rbt-badge-2">
                        <div className="image">🎉</div> {countText}
                    </span>
                )}
            </div>
            <p className="description">{description}</p>
        </>
    );
}

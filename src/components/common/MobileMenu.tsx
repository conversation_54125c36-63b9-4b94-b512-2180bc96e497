'use client';

import Category from '@/types/Category';
import Course from '@/types/Course';
import { ARTICLE_ROUTE, COURSE_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { startsWith } from 'lodash';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import CourseMenuItem from './CourseMenuItem';
import CourseMenuSide from './CourseMenuSide';

interface IProps {
    categories: Category[];
    pCategories: Category[];
    highlightCourses: Course[];
    pathname: string;
}

export default function MobileMenu({ categories, pCategories, highlightCourses, pathname }: Readonly<IProps>) {
    const searchParams = useSearchParams();
    const [menuId, setMenuId] = useState(0);

    useEffect(() => document.querySelector('#popup-mobile-menu')?.classList.remove('active'), [pathname]);

    const toggleMenu = (id: number) => {
        if (id === menuId) setMenuId(0);
        else setMenuId(id);
    };

    return (
        <>
            <nav className="mainmenu-nav">
                <ul className="mainmenu">
                    <li className="with-megamenu position-static">
                        <Link href="/" className={classNames({ active: pathname === '/' })}>
                            Trang chủ
                        </Link>
                    </li>
                    <li className="with-megamenu has-menu-child-item position-static">
                        <a
                            className={classNames('cursor-pointer', { open: menuId === 2 })}
                            onClick={() => toggleMenu(2)}
                        >
                            Nổi bật <i className="feather-chevron-down" />
                        </a>
                        <div className={classNames('rbt-megamenu menu-skin-dark', { 'active block': menuId === 2 })}>
                            <div className="wrapper">
                                <div className="row row--15 home-plesentation-wrapper single-dropdown-menu-presentation">
                                    {highlightCourses.map((item) => (
                                        <CourseMenuItem key={item.id} item={item} />
                                    ))}
                                </div>
                                <div className="load-demo-btn text-center">
                                    <a className="rbt-btn-link color-white" href="#">
                                        Scroll to view more{' '}
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width={16}
                                            height={16}
                                            fill="currentColor"
                                            className="bi bi-arrow-down-up"
                                            viewBox="0 0 16 16"
                                        >
                                            <path
                                                fillRule="evenodd"
                                                d="M11.5 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L11 2.707V14.5a.5.5 0 0 0 .5.5zm-7-14a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L4 13.293V1.5a.5.5 0 0 1 .5-.5z"
                                            />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li className="with-megamenu has-menu-child-item">
                        <a
                            className={classNames('cursor-pointer', {
                                open: menuId === 3,
                                active: startsWith(pathname, COURSE_ROUTE),
                            })}
                            onClick={() => toggleMenu(3)}
                        >
                            Khoá học <i className="feather-chevron-down" />
                        </a>
                        <div className={classNames('rbt-megamenu grid-item-2', { 'active block': menuId === 3 })}>
                            <div className="wrapper">
                                <div className="row row--15">
                                    <CourseMenuSide
                                        isOffline={false}
                                        items={highlightCourses.filter((item) => !item.is_offline).slice(0, 7)}
                                    />
                                    <CourseMenuSide
                                        isOffline={true}
                                        items={highlightCourses.filter((item) => item.is_offline).slice(0, 7)}
                                    />
                                </div>
                            </div>
                        </div>
                    </li>
                    <li className="has-dropdown has-menu-child-item">
                        <a
                            className={classNames('cursor-pointer', {
                                open: menuId === 1,
                            })}
                            onClick={() => toggleMenu(1)}
                        >
                            Thể loại
                            <i className="feather-chevron-down" />
                        </a>
                        <ul className={classNames('submenu', { 'active block': menuId === 1 })}>
                            {categories.map((item) => (
                                <li key={item.id}>
                                    <Link href={`/the-loai/${item.slug}-${item.id}`}>{item.title}</Link>
                                </li>
                            ))}
                        </ul>
                    </li>
                    <li className="has-dropdown has-menu-child-item">
                        <a
                            className={classNames('cursor-pointer', {
                                open: menuId === 4,
                            })}
                            onClick={() => toggleMenu(4)}
                        >
                            Sản phẩm
                            <i className="feather-chevron-down" />
                        </a>
                        <ul className={classNames('submenu', { 'active block': menuId === 4 })}>
                            {pCategories.map((item) => (
                                <li key={item.id}>
                                    <Link href={`/chuyen-muc/${item.slug}-${item.id}`}>{item.title}</Link>
                                </li>
                            ))}
                        </ul>
                    </li>
                    <li className="with-megamenu position-static">
                        <Link
                            href={ARTICLE_ROUTE}
                            className={classNames({ active: startsWith(pathname, ARTICLE_ROUTE) })}
                        >
                            Tin tức
                        </Link>
                    </li>
                    <li className="with-megamenu position-static">
                        <Link href="/lien-he" className={classNames({ active: pathname === '/lien-he' })}>
                            Liên hệ
                        </Link>
                    </li>
                </ul>
            </nav>
            <input type="text" id="pathname" hidden defaultValue={pathname} />
            <input type="text" id="highlightCourse" hidden defaultValue={searchParams?.get('highlight') ?? ''} />
        </>
    );
}

'use client';

import Category from '@/types/Category';
import { CourseSorts } from '@/types/Course';
import { SearchProduct } from '@/types/Product';
import { formatNumber, getSearchProductRoute } from '@/utils';
import { find } from 'lodash';
import { useRouter } from 'next/navigation';

interface IProps {
    categories: Category[];
    searchParams: SearchProduct;
}

export default function ProductSearchForm({ categories, searchParams }: Readonly<IProps>) {
    const router = useRouter();

    const changeCategory = (cId: number, checked: boolean) => {
        if (checked) {
            router.push(
                getSearchProductRoute({
                    ...searchParams,
                    categoryId: cId,
                    slug: find(categories, { id: cId })?.slug,
                    page: 1,
                })
            );
        } else {
            router.push(
                getSearchProductRoute({
                    ...searchParams,
                    categoryId: undefined,
                    slug: undefined,
                    page: 1,
                })
            );
        }
    };

    const changePriceRange = (priceRange: number, checked: boolean) => {
        if (checked) {
            router.push(
                getSearchProductRoute({
                    ...searchParams,
                    priceRange,
                    page: 1,
                })
            );
        } else {
            router.push(
                getSearchProductRoute({
                    ...searchParams,
                    priceRange: undefined,
                    page: 1,
                })
            );
        }
    };

    const changeSortBy = (sortBy: number, checked: boolean) => {
        if (checked) {
            router.push(
                getSearchProductRoute({
                    ...searchParams,
                    sortBy,
                    page: 1,
                })
            );
        }
    };

    return (
        <aside className="rbt-sidebar-widget-wrapper">
            <div className="rbt-single-widget rbt-widget-search">
                <div className="inner">
                    <form action={getSearchProductRoute({ ...searchParams, page: 1 })} className="rbt-search-style-1">
                        <input
                            type="text"
                            name="q"
                            defaultValue={searchParams.q ?? ''}
                            placeholder="Tìm kiếm sản phẩm"
                        />
                        <button className="search-btn">
                            <i className="feather-search"></i>
                        </button>
                    </form>
                </div>
            </div>
            <div className="rbt-single-widget rbt-widget-categories has-show-more active">
                <div className="inner">
                    <h4 className="rbt-widget-title">Chuyên mục</h4>
                    <ul className="rbt-sidebar-list-wrapper categories-list-check has-show-more-inner-content">
                        {categories.map((item) => (
                            <li key={item.id} className="rbt-check-group">
                                <input
                                    id={`category-${item.id}`}
                                    type="checkbox"
                                    value={item.id}
                                    checked={searchParams.categoryId === item.id}
                                    onChange={(e) => changeCategory(+e.target.value, e.target.checked)}
                                />
                                <label htmlFor={`category-${item.id}`}>{item.title}</label>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
            <div className="rbt-single-widget rbt-widget-prices">
                <div className="inner">
                    <h4 className="rbt-widget-title">Khoảng giá</h4>
                    <ul className="rbt-sidebar-list-wrapper prices-list-check">
                        {[...Array.from([0, 1, 2, 3, 4, 5, 6])].map((item) => (
                            <li key={item} className="rbt-check-group">
                                <input
                                    id={`price-${item}`}
                                    type="checkbox"
                                    value={item}
                                    checked={searchParams.priceRange === item}
                                    onChange={(e) => changePriceRange(+e.target.value, e.target.checked)}
                                />
                                <label htmlFor={`price-${item}`}>
                                    {item > 0
                                        ? `${formatNumber((item - 1) * 500000)} - ${formatNumber(item * 500000)}`
                                        : 'Miễn phí'}
                                </label>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
            <div className="rbt-single-widget rbt-widget-prices">
                <div className="inner">
                    <h4 className="rbt-widget-title">Sắp xếp theo</h4>
                    <ul className="rbt-sidebar-list-wrapper prices-list-check">
                        {CourseSorts.map((item) => (
                            <li key={item.value} className="rbt-check-group">
                                <input
                                    id={`sort-${item.value}`}
                                    type="checkbox"
                                    value={item.value}
                                    checked={(searchParams.sortBy ?? 0) === item.value}
                                    onChange={(e) => changeSortBy(+e.target.value, e.target.checked)}
                                />
                                <label htmlFor={`sort-${item}`}>{item.label}</label>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
        </aside>
    );
}

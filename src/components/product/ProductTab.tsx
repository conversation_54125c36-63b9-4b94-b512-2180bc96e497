'use client';

import ItemText from '@/components/common/ItemText';
import { useState } from 'react';
import classNames from 'classnames';

interface IProps {
    content: string;
}

export default function ProductTab({ content }: Readonly<IProps>) {
    const [tab, setTab] = useState(1);

    return (
        <div className="rbt-product-description rbt-section-gapBottom bg-color-white">
            <div className="container">
                <div className="row">
                    <div className="col-lg-8 offset-lg-2">
                        <ul className="nav nav-tabs tab-button-style-2">
                            <li className="nav-item">
                                <a
                                    className={classNames('tab-button cursor-pointer', { active: tab === 1 })}
                                    onClick={() => setTab(1)}
                                >
                                    <span className="title">Mô tả</span>
                                </a>
                            </li>
                            {/*<li className="nav-item">*/}
                            {/*    <a*/}
                            {/*        className={classNames('tab-button cursor-pointer', { active: tab === 2 })}*/}
                            {/*        onClick={() => setTab(2)}*/}
                            {/*    >*/}
                            {/*        <span className="title">Đánh giá</span>*/}
                            {/*    </a>*/}
                            {/*</li>*/}
                        </ul>
                        <div className="tab-content">
                            <div
                                className={classNames('product-description-content tab-pane fade', {
                                    'active show': tab === 1,
                                })}
                            >
                                <ItemText text={content} className={[]} />
                            </div>
                            {/*<div*/}
                            {/*    className={classNames('product-description-content tab-pane fade', {*/}
                            {/*        'active show': tab === 2,*/}
                            {/*    })}*/}
                            {/*>*/}
                            {/*    <ul className="comment-list">*/}
                            {/*        <li className="comment">*/}
                            {/*            <div className="comment-body">*/}
                            {/*                <div className="single-comment">*/}
                            {/*                    <div className="comment-img">*/}
                            {/*                        <img*/}
                            {/*                            src="assets/images/testimonial/testimonial-1.jpg"*/}
                            {/*                            alt="Author Images"*/}
                            {/*                        />*/}
                            {/*                    </div>*/}
                            {/*                    <div className="comment-inner">*/}
                            {/*                        <h6 className="commenter">*/}
                            {/*                            <a href="#">Cameron Williamson</a>*/}
                            {/*                        </h6>*/}
                            {/*                        <div className="comment-meta">*/}
                            {/*                            <div className="time-spent">Nov 23, 2018 at 12:23 pm</div>*/}
                            {/*                        </div>*/}
                            {/*                        <div className="rbt-review justify-content-start mb--20">*/}
                            {/*                            <div className="rating">*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                            </div>*/}
                            {/*                        </div>*/}
                            {/*                        <div className="comment-text">*/}
                            {/*                            <p className="b2">*/}
                            {/*                                Duis hendrerit velit scelerisque felis tempus, id porta*/}
                            {/*                                libero venenatis. Nulla facilisi. Phasellus viverra magna*/}
                            {/*                                commodo dui lacinia tempus. Donec malesuada nunc non dui*/}
                            {/*                                posuere, fringilla vestibulum urna mollis. Integer*/}
                            {/*                                condimentum ac sapien quis maximus.{' '}*/}
                            {/*                            </p>*/}
                            {/*                        </div>*/}
                            {/*                    </div>*/}
                            {/*                </div>*/}
                            {/*            </div>*/}
                            {/*        </li>*/}
                            {/*        <li className="comment">*/}
                            {/*            <div className="comment-body">*/}
                            {/*                <div className="single-comment">*/}
                            {/*                    <div className="comment-img">*/}
                            {/*                        <img*/}
                            {/*                            src="assets/images/testimonial/testimonial-3.jpg"*/}
                            {/*                            alt="Author Images"*/}
                            {/*                        />*/}
                            {/*                    </div>*/}
                            {/*                    <div className="comment-inner">*/}
                            {/*                        <h6 className="commenter">*/}
                            {/*                            <a href="#">Rafin Shuvo</a>*/}
                            {/*                        </h6>*/}
                            {/*                        <div className="comment-meta">*/}
                            {/*                            <div className="time-spent">Nov 23, 2018 at 12:23 pm</div>*/}
                            {/*                        </div>*/}
                            {/*                        <div className="rbt-review justify-content-start mb--20">*/}
                            {/*                            <div className="rating">*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                                <i className="fas fa-star"></i>*/}
                            {/*                            </div>*/}
                            {/*                        </div>*/}
                            {/*                        <div className="comment-text">*/}
                            {/*                            <p className="b2">*/}
                            {/*                                Duis hendrerit velit scelerisque felis tempus, id porta*/}
                            {/*                                libero venenatis. Nulla facilisi. Phasellus viverra magna*/}
                            {/*                                commodo dui lacinia tempus. Donec malesuada nunc non dui*/}
                            {/*                                posuere, fringilla vestibulum urna mollis. Integer*/}
                            {/*                                condimentum ac sapien quis maximus.{' '}*/}
                            {/*                            </p>*/}
                            {/*                        </div>*/}
                            {/*                    </div>*/}
                            {/*                </div>*/}
                            {/*            </div>*/}
                            {/*        </li>*/}
                            {/*    </ul>*/}
                            {/*    <div className="rbt-comment-form mt--50 rbt-shadow-box">*/}
                            {/*        <div className="comment-form-inner">*/}
                            {/*            <h3 className="title">Add Review</h3>*/}
                            {/*        </div>*/}
                            {/*        <form className="comment-form-style-1 position-relative" action="#">*/}
                            {/*            <p className="comment-note mb--20">*/}
                            {/*                Your email address will not be published. Required fields are marked **/}
                            {/*            </p>*/}

                            {/*            <div className="notification-text d-flex align-items-center mb--30">*/}
                            {/*                <h6 className="mb--0 fontWeight600 title">Your Rating</h6>*/}
                            {/*                <div className="rbt-review justify-content-start">*/}
                            {/*                    <div className="rating">*/}
                            {/*                        <i className="fas fa-star"></i>*/}
                            {/*                        <i className="fas fa-star"></i>*/}
                            {/*                        <i className="fas fa-star"></i>*/}
                            {/*                        <i className="fas fa-star"></i>*/}
                            {/*                        <i className="fas fa-star"></i>*/}
                            {/*                    </div>*/}
                            {/*                </div>*/}
                            {/*            </div>*/}

                            {/*            <div className="row row--10">*/}
                            {/*                <div className="col-lg-4 col-md-4 col-12">*/}
                            {/*                    <div className="form-group">*/}
                            {/*                        <label htmlFor="name">Your Name</label>*/}
                            {/*                        <input id="name" type="text" />*/}
                            {/*                    </div>*/}
                            {/*                </div>*/}
                            {/*                <div className="col-lg-4 col-md-4 col-12">*/}
                            {/*                    <div className="form-group">*/}
                            {/*                        <label htmlFor="bl-email">Your Email</label>*/}
                            {/*                        <input id="bl-email" type="email" />*/}
                            {/*                    </div>*/}
                            {/*                </div>*/}
                            {/*                <div className="col-lg-4 col-md-4 col-12">*/}
                            {/*                    <div className="form-group">*/}
                            {/*                        <label htmlFor="website">Your Website</label>*/}
                            {/*                        <input id="website" type="text" />*/}
                            {/*                    </div>*/}
                            {/*                </div>*/}
                            {/*                <div className="col-12">*/}
                            {/*                    <div className="form-group">*/}
                            {/*                        <label htmlFor="message">Leave a Reply</label>*/}
                            {/*                        <textarea id="message" name="message"></textarea>*/}
                            {/*                    </div>*/}
                            {/*                </div>*/}
                            {/*                <div className="col-lg-12">*/}
                            {/*                    <p className="comment-form-cookies-consent mb--30">*/}
                            {/*                        <input*/}
                            {/*                            id="wp-comment-cookies-consent"*/}
                            {/*                            name="wp-comment-cookies-consent"*/}
                            {/*                            type="checkbox"*/}
                            {/*                            value="yes"*/}
                            {/*                        />*/}
                            {/*                        <label htmlFor="wp-comment-cookies-consent">*/}
                            {/*                            Save my name, email, and website in this browser for the next*/}
                            {/*                            time I comment.*/}
                            {/*                        </label>*/}
                            {/*                    </p>*/}
                            {/*                </div>*/}
                            {/*                <div className="col-lg-12">*/}
                            {/*                    <button className="rbt-btn btn-gradient hover-icon-reverse">*/}
                            {/*                        <span className="icon-reverse-wrapper">*/}
                            {/*                            <span className="btn-text">Post Comment</span>*/}
                            {/*                            <span className="btn-icon">*/}
                            {/*                                <i className="feather-arrow-right"></i>*/}
                            {/*                            </span>*/}
                            {/*                            <span className="btn-icon">*/}
                            {/*                                <i className="feather-arrow-right"></i>*/}
                            {/*                            </span>*/}
                            {/*                        </span>*/}
                            {/*                    </button>*/}
                            {/*                </div>*/}
                            {/*            </div>*/}
                            {/*        </form>*/}
                            {/*    </div>*/}
                            {/*</div>*/}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

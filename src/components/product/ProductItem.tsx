import Product from '@/types/Product';
import { API_URL, PRODUCT_ROUTE } from '@/utils/constants';
import Link from 'next/link';
import CoursePrice from '@/components/common/CoursePrice';
import AddCartProduct from '@/components/common/AddCartProduct';

interface IProps {
    item: Product;
}

export default function ProductItem({ item }: Readonly<IProps>) {
    return (
        <div className="rbt-card variation-01 rbt-hover">
            <div className="rbt-card-img">
                <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>
                    <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                </Link>
            </div>
            <div className="rbt-card-body">
                <h4 className="rbt-card-title">
                    <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>{item.title}</Link>
                </h4>
                <div className="rbt-card-text">{item.description}</div>
                <div className="rbt-card-bottom">
                    <ul className="social-icon social-default transparent-with-border justify-content-start">
                        <AddCartProduct productId={item.documentId} />
                        <li className="li-product-price">
                            <CoursePrice price={item.price} salePrice={item.sale_price} />
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    );
}

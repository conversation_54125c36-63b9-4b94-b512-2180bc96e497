'use client';

import React, { useEffect, useState } from 'react';
import { useCartStore } from '@/stores/cartStore';
import { useRouter } from 'next/navigation';
import { find } from 'lodash';

interface IProps {
    productId: string;
}

export default function AddCartMainProduct({ productId }: Readonly<IProps>) {
    const [quantity, setQuantity] = useState<string | number>(1);
    const cartProducts = useCartStore((state) => state.products);
    const setCartProducts = useCartStore((state) => state.setProducts);
    const router = useRouter();

    useEffect(() => {
        const item = find(cartProducts, (item) => item.label === productId);
        if (item) setQuantity(item.value);
        else setQuantity(1);
    }, [cartProducts, productId]);

    const onChangeQuantity = (value: string) => {
        if (/^\d*$/.test(value)) {
            setQuantity(value === '' ? '' : +value > 99 ? 99 : +value);
        }
    };

    const onAdjustQuantity = (flag: boolean) => {
        let value = quantity === '' ? 1 : +quantity;
        if (flag) {
            value = value >= 99 ? 99 : value + 1;
        } else {
            value = value <= 1 ? 1 : value - 1;
        }
        setQuantity(value);
    };

    const addCart = (isRedirect: boolean) => {
        const value = quantity === '' ? 1 : +quantity;
        if (!isRedirect) {
            setCartProducts([
                ...cartProducts,
                {
                    label: productId,
                    value,
                },
            ]);
        } else {
            const items = [...cartProducts];
            const index = items.findIndex((item) => item.label === productId);
            if (index !== -1) {
                items[index].value = value;
                setCartProducts(items);
            }
            router.push(`/thanh-toan?pid=${productId}`);
        }
    };

    return (
        <div className="product-action mb--20">
            <div className="pro-qty">
                <span className="dec qtybtn" onClick={() => onAdjustQuantity(false)}>
                    -
                </span>
                <input type="text" value={quantity} onChange={(e) => onChangeQuantity(e.target.value)} />
                <span className="inc qtybtn" onClick={() => onAdjustQuantity(true)}>
                    +
                </span>
            </div>
            <div className="addto-cart-btn">
                <a
                    className="rbt-btn btn-gradient hover-icon-reverse cursor-pointer"
                    onClick={() => addCart(!!cartProducts.find((item) => item.label === productId))}
                >
                    <span className="icon-reverse-wrapper">
                        <span className="btn-text">
                            {!cartProducts.find((item) => item.label === productId) ? 'Thêm vào giỏ' : 'Mua ngay'}
                        </span>
                        <span className="btn-icon">
                            <i className="feather-arrow-right"></i>
                        </span>
                        <span className="btn-icon">
                            <i className="feather-arrow-right"></i>
                        </span>
                    </span>
                </a>
            </div>
        </div>
    );
}

import BookmarkCourse from '@/types/BookmarkCourse';
import Course from '@/types/Course';
import { GlobalConfig } from '@/types/GlobalConfig';
import { COURSE_ROUTE } from '@/utils/constants';
import Link from 'next/link';
import CourseItem from '../common/CourseItem';

interface IProps {
    items: Course[];
    userId: number;
    bookmarks: BookmarkCourse[];
    config: GlobalConfig | null;
}

export default function HomeCourses({ items, userId, bookmarks, config }: Readonly<IProps>) {
    return (
        <div className="rbt-course-area bg-color-extra2 rbt-section-gap">
            <div className="container">
                <div className="row mb--60">
                    <div className="col-lg-12">
                        <div className="section-title text-center">
                            <span className="subtitle bg-secondary-opacity">K<PERSON><PERSON> học phổ biến nhất</span>
                            <h2 className="title">
                                H<PERSON>y trải nghiệm và thực hành để có một cuộc yêu tuyệt vời.
                                <br />
                                Khám phá hơn 100 khóa học online trực tuyến tại B & G.
                            </h2>
                        </div>
                    </div>
                </div>
                <div className="row g-5">
                    {items.map((item) => (
                        <div key={item.id} className="col-lg-4 col-md-6 col-12">
                            <CourseItem item={item} userId={userId} bookmarks={bookmarks} config={config} />
                        </div>
                    ))}
                </div>
                <div className="row">
                    <div className="col-lg-12">
                        <div className="load-more-btn mt--60 text-center">
                            <Link className="rbt-btn btn-gradient btn-lg hover-icon-reverse" href={COURSE_ROUTE}>
                                <span className="icon-reverse-wrapper">
                                    <span className="btn-text">Tất cả khoá học</span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right"></i>
                                    </span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right"></i>
                                    </span>
                                </span>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

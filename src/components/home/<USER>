import Article from '@/types/Article';
import { API_URL, ARTICLE_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import Link from 'next/link';

interface IProps {
    items: Article[];
}

export default function HomeBlog({ items }: Readonly<IProps>) {
    return isEmpty(items) ? (
        <></>
    ) : (
        <div className="rbt-rbt-blog-area rbt-section-gap bg-color-extra2">
            <div className="container">
                <div className="row g-5 align-items-center mb--30">
                    <div className="col-lg-6 col-md-6 col-12">
                        <div className="section-title">
                            <span className="subtitle bg-pink-opacity">Tin tức</span>
                            <h2 className="title">Tin tức mới nhất tại B & G</h2>
                        </div>
                    </div>
                    <div className="col-lg-6 col-md-6 col-12">
                        <div className="read-more-btn text-start text-md-end">
                            <Link className="rbt-btn btn-gradient hover-icon-reverse" href={ARTICLE_ROUTE}>
                                <div className="icon-reverse-wrapper">
                                    <span className="btn-text">Xem tất cả</span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right" />
                                    </span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right" />
                                    </span>
                                </div>
                            </Link>
                        </div>
                    </div>
                </div>
                <div className="row row--15">
                    <div className="col-lg-6 col-md-12 col-sm-12 col-12 mt--30 sal-animate">
                        <div className="rbt-card variation-02 height-330 rbt-hover">
                            <div className="rbt-card-img">
                                <Link href={`${ARTICLE_ROUTE}/${items[0].slug}-${items[0].documentId}`}>
                                    <img src={`${API_URL}${items[0].image.url}`} alt={items[0].title} />
                                </Link>
                            </div>
                            <div className="rbt-card-body">
                                <h3 className="rbt-card-title">
                                    <Link href={`${ARTICLE_ROUTE}/${items[0].slug}-${items[0].documentId}`}>
                                        {items[0].title}
                                    </Link>
                                </h3>
                                <p className="rbt-card-text">{items[0].description}</p>
                                <div className="rbt-card-bottom">
                                    <Link
                                        className="transparent-button"
                                        href={`${ARTICLE_ROUTE}/${items[0].slug}-${items[0].documentId}`}
                                    >
                                        Đọc thêm
                                        <i>
                                            <svg width={17} height={12} xmlns="http://www.w3.org/2000/svg">
                                                <g stroke="#27374D" fill="none" fillRule="evenodd">
                                                    <path d="M10.614 0l5.629 5.629-5.63 5.629" />
                                                    <path strokeLinecap="square" d="M.663 5.572h14.594" />
                                                </g>
                                            </svg>
                                        </i>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="col-lg-6 col-md-12 col-sm-12 col-12 mt--30 sal-animate">
                        {items
                            .filter((_, index) => index > 0)
                            .map((item, index) => (
                                <div
                                    key={item.id}
                                    className={classNames('rbt-card card-list variation-02 rbt-hover', {
                                        'mt--30': index > 0,
                                    })}
                                >
                                    <div className="rbt-card-img">
                                        <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>
                                            <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                                        </Link>
                                    </div>
                                    <div className="rbt-card-body">
                                        <h5 className="rbt-card-title">
                                            <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>
                                                {item.title}
                                            </Link>
                                        </h5>
                                        <div className="rbt-card-bottom">
                                            <Link
                                                className="transparent-button"
                                                href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}
                                            >
                                                Đọc thêm
                                                <i>
                                                    <svg width={17} height={12} xmlns="http://www.w3.org/2000/svg">
                                                        <g stroke="#27374D" fill="none" fillRule="evenodd">
                                                            <path d="M10.614 0l5.629 5.629-5.63 5.629" />
                                                            <path strokeLinecap="square" d="M.663 5.572h14.594" />
                                                        </g>
                                                    </svg>
                                                </i>
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

import { GlobalConfig } from '@/types/GlobalConfig';
import { COURSE_ROUTE } from '@/utils/constants';
import Link from 'next/link';

interface IProps {
    config: GlobalConfig | null;
}

export default function HomeSlider({ config }: Readonly<IProps>) {
    return (
        <div className="rbt-banner-5 height-650 bg_image bg_image--19" data-gradient-overlay="3">
            <div className="container">
                <div className="row">
                    <div className="col-md-12">
                        <div className="inner text-start">
                            <h2 className="title">
                                <span className="text-decoration-underline">B & G</span> Đ<PERSON> có được cuộc yêu tuyệt vời
                            </h2>
                            <p className="description">{config?.siteDesc}</p>
                            <div className="slider-btn rbt-button-group justify-content-start">
                                <Link
                                    className="rbt-btn btn-border icon-hover color-white radius-round"
                                    href={COURSE_ROUTE}
                                >
                                    <span className="btn-text"><PERSON><PERSON><PERSON> kho<PERSON> họ<PERSON></span>
                                    <span className="btn-icon">
                                        <i className="feather-arrow-right"></i>
                                    </span>
                                </Link>
                                <a className="rbt-btn-link color-white" href={`${COURSE_ROUTE}?priceRange=0`}>
                                    Thử miễn phí<i className="feather-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

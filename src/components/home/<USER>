'use client';

import Course from '@/types/Course';
import { isEmpty } from 'lodash';
import { useMemo } from 'react';
import 'swiper/css';
import { A11y, Navigation, Scrollbar } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { useWindowSize } from 'usehooks-ts';
import EventItem from '../common/EventItem';

interface IProps {
    items: Course[];
}

export default function HomeEvents({ items }: Readonly<IProps>) {
    const { width = 0 } = useWindowSize();

    const slidesPerView = useMemo(() => {
        if (width > 990) {
            return 3;
        }
        if (width > 760) {
            return 2;
        }
        return 1;
    }, [width]);

    return isEmpty(items) ? (
        <></>
    ) : (
        <div className="rbt-event-area rbt-section-gap bg-gradient-3">
            <div className="container">
                <div className="row mb--55">
                    <div className="section-title text-center">
                        <span className="subtitle bg-white-opacity"><PERSON><PERSON><PERSON> học tại <PERSON> viện B & G</span>
                        <h2 className="title color-white"><PERSON><PERSON><PERSON> học sắp diễn ra tại Học viện B & G</h2>
                    </div>
                </div>
                <div className="row">
                    <div className="col-lg-12">
                        <Swiper
                            modules={[Navigation, Scrollbar, A11y]}
                            spaceBetween={50}
                            slidesPerView={slidesPerView}
                            navigation
                            className="home-event-slider"
                        >
                            {items.map((item) => (
                                <SwiperSlide key={item.id}>
                                    <EventItem item={item} />
                                </SwiperSlide>
                            ))}
                        </Swiper>
                    </div>
                </div>
            </div>
        </div>
    );
}

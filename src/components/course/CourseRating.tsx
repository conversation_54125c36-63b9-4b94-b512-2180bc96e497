interface IProps {
    tag: string;
}

export default function CourseRating({ tag }: Readonly<IProps>) {
    return (
        <div className="d-flex align-items-center mb--20 flex-wrap rbt-course-details-feature">
            <div className="feature-sin best-seller-badge">
                <span className="rbt-badge-2">
                    <span className="image">
                        <img src="/assets/images/icons/card-icon-1.png" alt="" />
                    </span>{' '}
                    {tag}
                </span>
            </div>
            <div className="feature-sin rating">
                <a className="cursor-pointer">4.8</a>
                <a className="cursor-pointer">
                    <i className="fa fa-star" />
                </a>
                <a className="cursor-pointer">
                    <i className="fa fa-star" />
                </a>
                <a className="cursor-pointer">
                    <i className="fa fa-star" />
                </a>
                <a className="cursor-pointer">
                    <i className="fa fa-star" />
                </a>
                <a className="cursor-pointer">
                    <i className="fa fa-star" />
                </a>
            </div>
            {/* <div className="feature-sin total-rating">
                <a className="rbt-badge-4" href="#">
                    215,475 đánh giá
                </a>
            </div>
            <div className="feature-sin total-student">
                <span>616,029 học viên</span>
            </div> */}
        </div>
    );
}

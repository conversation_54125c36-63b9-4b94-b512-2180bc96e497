'use client';

import { Grow } from '@material-ui/core';
import { useState } from 'react';
import ModalVideo from 'react-modal-video';
import 'react-modal-video/scss/modal-video.scss';

interface IProps {
    image: string;
    video: string;
}
export default function VideoPreview({ image, video }: Readonly<IProps>) {
    const [show, setShow] = useState(false);

    return (
        <>
            <a
                className="video-popup-with-text video-popup-wrapper text-center popup-video sidebar-video-hidden mb--15 cursor-pointer"
                onClick={() => setShow(true)}
            >
                <div className="video-content">
                    <img className="w-100 rbt-radius" src={image} alt="" />
                    <div className="position-to-top">
                        <span className="rbt-btn rounded-player-2 with-animation">
                            <span className="play-icon" />
                        </span>
                    </div>
                    <span className="play-view-text d-block color-white">
                        <i className="feather-eye" /> Video giới thiệu
                    </span>
                </div>
            </a>
            <Grow in={show} mountOnEnter unmountOnExit>
                <ModalVideo
                    channel="youtube"
                    isOpen={true}
                    videoId={video ?? 'r0svGYNFswc'}
                    onClose={() => setShow(false)}
                />
            </Grow>
        </>
    );
}

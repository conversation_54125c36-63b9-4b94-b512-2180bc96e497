'use client';

import { useCartStore } from '@/stores/cartStore';
import { showToast } from '@/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface IProps {
    courseId: string;
}

export default function AddCartCourse({ courseId }: Readonly<IProps>) {
    const cartCourseIds = useCartStore((state) => state.courseIds);
    const setCartCourseIds = useCartStore((state) => state.setCourseIds);
    const router = useRouter();

    const addCart = (isRedirect: boolean) => {
        if (!cartCourseIds.includes(courseId)) {
            setCartCourseIds([...cartCourseIds, courseId]);
            showToast(true, 'Đã thêm vào giỏ hàng');
        }
        if (isRedirect) router.push(`/thanh-toan?id=${courseId}`);
    };

    return !cartCourseIds.includes(courseId) ? (
        <>
            <div className="add-to-card-button mt--15">
                <a
                    className="rbt-btn btn-border icon-hover w-100 d-block text-center cursor-pointer"
                    onClick={() => addCart(false)}
                >
                    <span className="btn-text">Thêm vào giỏ</span>
                    <span className="btn-icon">
                        <i className="feather-arrow-right" />
                    </span>
                </a>
            </div>
            <div className="buy-now-btn mt--15">
                <a
                    className="rbt-btn btn-gradient icon-hover w-100 d-block text-center cursor-pointer"
                    onClick={() => addCart(true)}
                >
                    <span className="btn-text">Mua ngay</span>
                    <span className="btn-icon">
                        <i className="feather-arrow-right" />
                    </span>
                </a>
            </div>
        </>
    ) : (
        <div className="buy-now-btn mt--15">
            <Link className="rbt-btn btn-gradient icon-hover w-100 d-block text-center" href="/thanh-toan">
                <span className="btn-text">Mua ngay</span>
                <span className="btn-icon">
                    <i className="feather-arrow-right" />
                </span>
            </Link>
        </div>
    );
}

'use client';

import { useCartStore } from '@/stores/cartStore';
import Course from '@/types/Course';
import UserCourse from '@/types/UserCourse';
import { formatNumber } from '@/utils';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface IProps {
    item: Course;
    userCourses: UserCourse[];
}

export default function CoursePriceBottom({ item, userCourses }: Readonly<IProps>) {
    const [show, setShow] = useState(true);
    const cartCourseIds = useCartStore((state) => state.courseIds);
    const setCartCourseIds = useCartStore((state) => state.setCourseIds);
    const router = useRouter();

    const onScroll = () => setShow(window.pageYOffset > 2400);

    useEffect(() => {
        window.addEventListener('scroll', onScroll);
        return () => window.removeEventListener('scroll', onScroll);
    }, []);

    const addCart = () => {
        if (!cartCourseIds.includes(item.documentId)) {
            setCartCourseIds([...cartCourseIds, item.documentId]);
        }
        router.push(`/thanh-toan?id=${item.documentId}`);
    };

    return (
        <div className={classNames('rbt-course-action-bottom', { 'rbt-course-action-active': show })}>
            <div className="container">
                <div className="row align-items-center">
                    <div className="col-lg-6 col-md-6">
                        <div className="section-title text-center text-md-start">
                            <h5 className="title mb--0">{item.title}</h5>
                        </div>
                    </div>
                    <div className="col-lg-6 col-md-6 mt_sm--15">
                        <div className="course-action-bottom-right rbt-single-group">
                            <div className="rbt-single-list rbt-price large-size justify-content-center">
                                <span className="current-price color-primary">{formatNumber(item.sale_price)}đ</span>
                                <span className="off-price">{formatNumber(item.price)}đ</span>
                            </div>
                            <div className="rbt-single-list action-btn">
                                {!isEmpty(userCourses) ? (
                                    <>
                                        {userCourses[0].approved ? (
                                            <Link
                                                className="rbt-btn btn-gradient icon-hover w-100 d-block text-center"
                                                href={`/hoc-bai/${item.slug}-${item.documentId}/${item.chapters[0].id}/${item.chapters[0].Lessions?.[0].id}`}
                                            >
                                                <span className="btn-text">Vào học</span>
                                                <span className="btn-icon">
                                                    <i className="feather-arrow-right" />
                                                </span>
                                            </Link>
                                        ) : (
                                            <a className="rbt-btn btn-gradient btn-gradient-2 cursor-pointer w-100 d-block text-center">
                                                Chờ xác nhận
                                            </a>
                                        )}
                                    </>
                                ) : (
                                    <a
                                        className="rbt-btn btn-gradient hover-icon-reverse btn-md cursor-pointer"
                                        onClick={addCart}
                                    >
                                        <span className="icon-reverse-wrapper">
                                            <span className="btn-text">Mua ngay</span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                            <span className="btn-icon">
                                                <i className="feather-arrow-right" />
                                            </span>
                                        </span>
                                    </a>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

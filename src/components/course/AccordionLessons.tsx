'use client';

import { Chapter } from '@/types/Course';
import classNames from 'classnames';
import Link from 'next/link';
import { useState } from 'react';

interface IProps {
    slug: string;
    chapterId: number;
    lessonId: number;
    chapters: Chapter[];
}

export default function AccordionLessons({ slug, chapterId, lessonId, chapters }: Readonly<IProps>) {
    const [chapId, setChapId] = useState(chapterId);

    return (
        <div className="rbt-course-feature-inner rbt-search-activation">
            <div className="section-title">
                <h4 className="rbt-title-style-3">Nội dung khoá học</h4>
            </div>
            {/* <div className="lesson-search-wrapper">
                <div className="rbt-search-style-1">
                    <input className="rbt-search-active" type="text" placeholder="Tìm khoá học" />
                    <button className="search-btn">
                        <i className="feather-search" />
                    </button>
                </div>
            </div>
            <hr className="mt--10" /> */}
            <div className="rbt-accordion-style rbt-accordion-02 for-right-content accordion">
                <div className="accordion">
                    {chapters.map((chapter, index) => (
                        <div key={chapter.id} className="accordion-item card">
                            <h2 className="accordion-header card-header">
                                <button
                                    className={classNames('accordion-button', {
                                        collapsed: chapId !== chapter.id,
                                    })}
                                    type="button"
                                    onClick={() => {
                                        if (chapId !== chapter.id) setChapId(chapter.id);
                                        else setChapId(0);
                                    }}
                                >
                                    {index + 1}. {chapter.title}{' '}
                                    {chapter.duration && <span className="rbt-badge-5 ml--10">{chapter.duration}</span>}
                                </button>
                            </h2>
                            <div
                                className={classNames('accordion-collapse collapse', {
                                    show: chapId === chapter.id,
                                })}
                            >
                                <div className="accordion-body card-body">
                                    <ul className="rbt-course-main-content liststyle">
                                        {chapter.Lessions?.map((lesson) => (
                                            <li key={lesson.id}>
                                                <Link
                                                    href={`/hoc-bai/${slug}/${chapter.id}/${lesson.id}`}
                                                    className={classNames({ 'color-primary': lessonId === lesson.id })}
                                                >
                                                    <div className="course-content-left">
                                                        <i className="feather-play-circle" />{' '}
                                                        <span className="text">{lesson.title}</span>
                                                    </div>
                                                    <div className="course-content-right">
                                                        {lesson.duration && (
                                                            <span className="min-lable">{lesson.duration}</span>
                                                        )}
                                                        {/* <span className="rbt-check">
                                                            <i className="feather-check" />
                                                        </span> */}
                                                    </div>
                                                </Link>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

import { Tag } from '@/types/common';
import { isEmpty } from 'lodash';
import Tags from '../common/Tags';

interface IProps {
    items: Tag[];
    tags: Tag[];
}

export default function CourseChecklist({ items, tags }: Readonly<IProps>) {
    return (
        <div className="rbt-course-feature-box rbt-shadow-box details-wrapper mt--30" id="overview">
            <div className="row g-5">
                <div className="col-lg-12">
                    <div className="section-title">
                        <h4 className="rbt-title-style-3 mb--20">Bạn sẽ học được</h4>
                    </div>
                    <ul className="rbt-list-style-1">
                        {items.map((item) => (
                            <li key={item.id}>
                                <i className="feather-check" />
                                {item.title}
                            </li>
                        ))}
                    </ul>
                    {!isEmpty(tags) && (
                        <>
                            <hr className="mt--20" />
                            <div className="tagcloud">
                                <Tags tags={tags} />
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
}

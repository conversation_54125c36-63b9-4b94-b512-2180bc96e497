'use client';

import Category from '@/types/Category';
import { CourseSorts, SearchCourse } from '@/types/Course';
import { formatNumber, getSearchCourseRoute } from '@/utils';
import { find } from 'lodash';
import { useRouter } from 'next/navigation';

interface IProps {
    categories: Category[];
    searchParams: SearchCourse;
}
export default function CourseSearchForm({ categories, searchParams }: Readonly<IProps>) {
    const router = useRouter();

    const changeCategory = (cId: number) =>
        router.push(
            getSearchCourseRoute({
                ...searchParams,
                categoryId: cId,
                slug: find(categories, { id: cId })?.slug,
                page: 1,
            })
        );

    const changeType = (isOffline: string) =>
        router.push(
            getSearchCourseRoute({
                ...searchParams,
                isOffline,
                page: 1,
            })
        );

    const changePriceRange = (priceRange: number) =>
        router.push(
            getSearchCourseRoute({
                ...searchParams,
                priceRange,
                page: 1,
            })
        );

    const changeSortBy = (sortBy: number) =>
        router.push(
            getSearchCourseRoute({
                ...searchParams,
                sortBy,
                page: 1,
            })
        );

    return (
        <div className="default-exp-wrapper">
            <div className="filter-inner">
                <div className="filter-select-option">
                    <div className="filter-select rbt-modern-select">
                        <span className="select-label d-block">Thể loại</span>
                        <select value={searchParams.categoryId ?? 0} onChange={(e) => changeCategory(+e.target.value)}>
                            <option value={0}>--Chọn--</option>
                            {categories.map((item) => (
                                <option key={item.id} value={item.id}>
                                    {item.title}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
                <div className="filter-select-option">
                    <div className="filter-select rbt-modern-select">
                        <span className="select-label d-block">Phương thức học</span>
                        <select value={searchParams.isOffline ?? '0'} onChange={(e) => changeType(e.target.value)}>
                            <option value="0">--Chọn--</option>
                            <option value="2">Online trực tuyến</option>
                            <option value="1">Tại trung tâm</option>
                        </select>
                    </div>
                </div>
                <div className="filter-select-option">
                    <div className="filter-select rbt-modern-select">
                        <span className="select-label d-block">Khoảng giá</span>
                        <select
                            value={searchParams.priceRange ?? '-1'}
                            onChange={(e) => changePriceRange(+e.target.value)}
                        >
                            <option value={-1}>--Chọn--</option>
                            {[...Array.from([0, 1, 2, 3, 4, 5, 6])].map((item) => (
                                <option key={item} value={item}>
                                    {item > 0
                                        ? `${formatNumber((item - 1) * 500000)} - ${formatNumber(item * 500000)}`
                                        : 'Miễn phí'}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
                <div className="filter-select-option">
                    <div className="filter-select rbt-modern-select">
                        <span className="select-label d-block">Sắp xếp theo</span>
                        <select value={searchParams.sortBy ?? 0} onChange={(e) => changeSortBy(+e.target.value)}>
                            {CourseSorts.map((item) => (
                                <option key={item.value} value={item.value}>
                                    {item.label}
                                </option>
                            ))}
                        </select>
                    </div>
                </div>
                <div className="filter-select-option" />
            </div>
        </div>
    );
}

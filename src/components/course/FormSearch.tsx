import { SearchCourse } from '@/types/Course';
import { getSearchCourseRoute } from '@/utils';

interface IProps {
    searchParams: SearchCourse;
}
export default function FormSearch({ searchParams }: Readonly<IProps>) {
    return (
        <div className="col-lg-7 col-md-12">
            <div className="rbt-sorting-list d-flex flex-wrap align-items-center justify-content-start justify-content-lg-end">
                <div className="rbt-short-item">
                    <form action={getSearchCourseRoute({ ...searchParams, page: 1 })} className="rbt-search-style me-0">
                        <input
                            type="text"
                            name="q"
                            defaultValue={searchParams.q ?? ''}
                            placeholder="Tìm kiếm khoá học"
                        />
                        <button type="submit" className="rbt-search-btn rbt-round-btn">
                            <i className="feather-search" />
                        </button>
                    </form>
                </div>
            </div>
        </div>
    );
}

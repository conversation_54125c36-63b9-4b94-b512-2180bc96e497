'use client';

import { Chapter } from '@/types/Course';
import { COURSE_ROUTE } from '@/utils/constants';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import Link from 'next/link';
import { useEffect, useState } from 'react';

interface IProps {
    chapters: Chapter[];
    approved: boolean;
    slug: string;
}

export default function CourseLessons({ chapters, approved, slug }: Readonly<IProps>) {
    const [chapterId, setChapterId] = useState(0);

    useEffect(() => {
        if (!isEmpty(chapters)) setChapterId(chapters[0].id);
    }, [chapters]);

    return (
        <div className="course-content rbt-shadow-box coursecontent-wrapper mt--30" id="content">
            <div className="rbt-course-feature-inner">
                <div className="section-title">
                    <h4 className="rbt-title-style-3">Nội dung kho<PERSON> học</h4>
                </div>
                <div className="rbt-accordion-style rbt-accordion-02 accordion">
                    <div className="accordion">
                        {chapters.map((chapter, index) => (
                            <div key={chapter.id} className="accordion-item card">
                                <h2 className="accordion-header card-header">
                                    <button
                                        className={classNames('accordion-button', {
                                            collapsed: chapterId !== chapter.id,
                                        })}
                                        type="button"
                                        onClick={() => {
                                            if (chapterId !== chapter.id) setChapterId(chapter.id);
                                            else setChapterId(0);
                                        }}
                                    >
                                        {index + 1}. {chapter.title}{' '}
                                        {chapter.duration && (
                                            <span className="rbt-badge-5 ml--10">{chapter.duration}</span>
                                        )}
                                    </button>
                                </h2>
                                <div
                                    className={classNames('accordion-collapse collapse', {
                                        show: chapterId === chapter.id,
                                    })}
                                >
                                    <div className="accordion-body card-body pr--0">
                                        <ul className="rbt-course-main-content liststyle">
                                            {chapter.Lessions?.map((lesson) => (
                                                <li key={lesson.id}>
                                                    <Link
                                                        href={
                                                            approved
                                                                ? `/hoc-bai/${slug}/${chapter.id}/${lesson.id}`
                                                                : `${COURSE_ROUTE}/${slug}`
                                                        }
                                                    >
                                                        <div className="course-content-left">
                                                            <i
                                                                className={`${lesson.video ? 'feather-play-circle' : 'feather-file-text'}`}
                                                            />
                                                            <span className="text">{lesson.title}</span>
                                                        </div>
                                                        <div className="course-content-right">
                                                            {lesson.is_preview ? (
                                                                <>
                                                                    {lesson.duration && (
                                                                        <span className="min-lable">
                                                                            {lesson.duration}
                                                                        </span>
                                                                    )}
                                                                    <span className="rbt-badge variation-03 bg-primary-opacity">
                                                                        <i className="feather-eye" /> Xem thử
                                                                    </span>
                                                                </>
                                                            ) : (
                                                                <span className="course-lock">
                                                                    <i className="feather-lock" />
                                                                </span>
                                                            )}
                                                        </div>
                                                    </Link>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

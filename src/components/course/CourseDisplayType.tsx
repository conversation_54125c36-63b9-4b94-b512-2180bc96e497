'use client';

import { SearchCourse } from '@/types/Course';
import { getSearchCourseRoute } from '@/utils';
import classNames from 'classnames';
import { useRouter } from 'next/navigation';

interface IProps {
    searchCourse: SearchCourse;
    courseCount: number;
}
export default function CourseDisplayType({ searchCourse, courseCount }: Readonly<IProps>) {
    const router = useRouter();

    return (
        <div className="col-lg-5 col-md-12">
            <div className="rbt-sorting-list d-flex flex-wrap align-items-center">
                {courseCount === -1 && (
                    <div className="rbt-short-item switch-layout-container">
                        <ul className="course-switch-layout">
                            <li className="course-switch-item">
                                <button
                                    className={classNames('rbt-grid-view', { active: !searchCourse.highlight })}
                                    title="Tất cả"
                                    onClick={() =>
                                        router.push(
                                            getSearchCourseRoute({ ...searchCourse, highlight: false, page: 1 })
                                        )
                                    }
                                >
                                    <i className="feather-grid" /> <span className="text">Tất cả</span>
                                </button>
                            </li>
                            <li className="course-switch-item">
                                <button
                                    className={classNames('rbt-list-view', { active: searchCourse.highlight })}
                                    title="Nổi bật"
                                    onClick={() =>
                                        router.push(getSearchCourseRoute({ ...searchCourse, highlight: true, page: 1 }))
                                    }
                                >
                                    <i className="feather-star" /> <span className="text">Nổi bật</span>
                                </button>
                            </li>
                        </ul>
                    </div>
                )}
                <div className="rbt-short-item switch-layout-container">
                    <ul className="course-switch-layout">
                        <li className="course-switch-item">
                            <button
                                className={classNames('rbt-grid-view', { active: !searchCourse.displayList })}
                                title="Grid Layout"
                                onClick={() =>
                                    router.push(getSearchCourseRoute({ ...searchCourse, displayList: false }))
                                }
                            >
                                <i className="feather-grid" /> <span className="text">Grid</span>
                            </button>
                        </li>
                        <li className="course-switch-item">
                            <button
                                className={classNames('rbt-list-view', { active: searchCourse.displayList })}
                                title="List Layout"
                                onClick={() =>
                                    router.push(getSearchCourseRoute({ ...searchCourse, displayList: true }))
                                }
                            >
                                <i className="feather-list" /> <span className="text">List</span>
                            </button>
                        </li>
                    </ul>
                </div>
                {courseCount > 0 && (
                    <div className="rbt-short-item">
                        <span className="course-index">Tìm thấy {courseCount} khoá học</span>
                    </div>
                )}
            </div>
        </div>
    );
}

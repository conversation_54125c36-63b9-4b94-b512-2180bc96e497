interface IProps {
    content: string;
}

export default function CourseOverview({ content }: Readonly<IProps>) {
    return (
        <div
            className="rbt-course-feature-box overview-wrapper rbt-shadow-box mt--30 has-show-more active"
            id="overview"
        >
            <div className="rbt-course-feature-inner has-show-more-inner-content">
                <div className="section-title">
                    <h4 className="rbt-title-style-3">Mô tả khoá học</h4>
                </div>
                {content}
            </div>
        </div>
    );
}

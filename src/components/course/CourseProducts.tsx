'use client';

import CoursePrice from '../common/CoursePrice';
import AddCartProduct from '@/components/common/AddCartProduct';
import Product from '@/types/Product';
import { API_URL, PRODUCT_ROUTE } from '@/utils/constants';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { isEmpty, uniq } from 'lodash';
import { getProducts } from '@/services/ProductService';
import ProductRating from '@/components/common/ProductRating';

interface IProps {
    productIds: string[];
}

export default function CourseProducts({ productIds }: Readonly<IProps>) {
    const [products, setProducts] = useState<Product[]>([]);

    useEffect(() => {
        const getList = async () => {
            const result = await getProducts({ documentIds: uniq(productIds) });
            setProducts(result.data ?? []);
        };
        if (!isEmpty(productIds)) getList();
    }, [productIds]);
    return (
        <div className="about-author-list rbt-shadow-box featured-wrapper mt--30" id="product">
            <div className="section-title">
                <h4 className="rbt-title-style-3">Sản phẩm</h4>
            </div>
            <div className="has-show-more-inner-content rbt-featured-review-list-wrapper">
                {products.map((item) => (
                    <div key={item.id} className="rbt-course-review about-author">
                        <div className="media">
                            <div className="thumbnail">
                                <Link href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}>
                                    <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                                </Link>
                            </div>
                            <div className="media-body">
                                <div className="author-info">
                                    <h5 className="title">
                                        <Link
                                            className="hover-flip-item-wrapper"
                                            href={`${PRODUCT_ROUTE}/${item.slug}-${item.documentId}`}
                                        >
                                            {item.title}
                                        </Link>
                                    </h5>
                                </div>
                                <div className="content">
                                    {/*<div className="description">{item.description}</div>*/}
                                    <ul className="social-icon social-default transparent-with-border justify-content-start mt--0">
                                        <AddCartProduct productId={item.documentId} />
                                        <li className="li-product-price">
                                            <CoursePrice price={item.price} salePrice={item.sale_price} />
                                        </li>
                                    </ul>
                                    <ProductRating />
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}

import Article from '@/types/Article';
import { API_URL, ARTICLE_ROUTE } from '@/utils/constants';
import { formatDateTime } from '@/utils/date';
import Link from 'next/link';

interface IProps {
    item: Article;
}

export default function BlogItem({ item }: Readonly<IProps>) {
    return (
        <div className="col-lg-6 col-md-6 col-12">
            <div className="rbt-blog-grid rbt-card variation-02 rbt-hover">
                <div className="rbt-card-img">
                    <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>
                        <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                    </Link>
                </div>
                <div className="rbt-card-body">
                    <h5 className="rbt-card-title">
                        <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>{item.title}</Link>
                    </h5>
                    <ul className="blog-meta">
                        <li>
                            <i className="feather-clock" /> {formatDateTime(item.publishedAt)}
                        </li>
                        <li>
                            <i className="feather-watch" /> 1 min read
                        </li>
                    </ul>
                    <p className="rbt-card-text">{item.description}</p>
                    <div className="rbt-card-bottom">
                        <Link className="transparent-button" href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>
                            Đọc thêm
                            <i>
                                <svg width={17} height={12} xmlns="http://www.w3.org/2000/svg">
                                    <g stroke="#27374D" fill="none" fillRule="evenodd">
                                        <path d="M10.614 0l5.629 5.629-5.63 5.629" />
                                        <path strokeLinecap="square" d="M.663 5.572h14.594" />
                                    </g>
                                </svg>
                            </i>
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}

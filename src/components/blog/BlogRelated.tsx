'use client';

import { getArticles } from '@/services/ArticleService';
import Article from '@/types/Article';
import { API_URL, ARTICLE_LIMIT, ARTICLE_ROUTE } from '@/utils/constants';
import { isEmpty } from 'lodash';
import Link from 'next/link';
import { useEffect, useState } from 'react';

interface IProps {
    id: number;
}

export default function BlogRelated({ id }: Readonly<IProps>) {
    const [articles, setArticles] = useState<Article[]>([]);

    useEffect(() => {
        const getItems = async () => {
            if (id > 0) {
                const response = await getArticles({ isPage: false, limit: ARTICLE_LIMIT, differentId: id });
                setArticles(response.data ?? []);
            }
        };
        getItems();
    }, [id]);

    return isEmpty(articles) ? (
        <></>
    ) : (
        <div className="related-post pt--60">
            <div className="section-title text-start mb--40">
                <span className="subtitle bg-primary-opacity"><PERSON><PERSON><PERSON> viện Yêu B & G</span>
                <h4 className="title"><PERSON><PERSON><PERSON> bài viết liên quan</h4>
            </div>
            {articles.map((item) => (
                <div key={item.id} className="rbt-card card-list variation-02 rbt-hover mt--30">
                    <div className="rbt-card-img">
                        <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>
                            <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                        </Link>
                    </div>
                    <div className="rbt-card-body">
                        <h5 className="rbt-card-title">
                            <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>{item.title}</Link>
                        </h5>
                        <div className="rbt-card-bottom">
                            <Link
                                className="transparent-button"
                                href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}
                            >
                                Đọc thêm
                                <i>
                                    <svg width={17} height={12} xmlns="http://www.w3.org/2000/svg">
                                        <g stroke="#27374D" fill="none" fillRule="evenodd">
                                            <path d="M10.614 0l5.629 5.629-5.63 5.629" />
                                            <path strokeLinecap="square" d="M.663 5.572h14.594" />
                                        </g>
                                    </svg>
                                </i>
                            </Link>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
}

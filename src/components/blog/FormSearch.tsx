import { BaseSearch } from '@/types/common';
import { getSearchRoute } from '@/utils';
import { ARTICLE_ROUTE } from '@/utils/constants';

interface IProps {
    searchParams: BaseSearch;
}
export default function FormSearch({ searchParams }: Readonly<IProps>) {
    return (
        <div className="rbt-single-widget rbt-widget-search">
            <div className="inner">
                <form
                    action={getSearchRoute(ARTICLE_ROUTE, { ...searchParams, page: 1 })}
                    className="rbt-search-style-1"
                >
                    <input type="text" name="q" defaultValue={searchParams.q ?? ''} placeholder="Tìm kiếm tin tức" />
                    <button className="search-btn">
                        <i className="feather-search" />
                    </button>
                </form>
            </div>
        </div>
    );
}

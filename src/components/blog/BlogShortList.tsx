import Article from '@/types/Article';
import { API_URL, ARTICLE_ROUTE } from '@/utils/constants';
import { formatDateTime } from '@/utils/date';
import { isEmpty } from 'lodash';
import Link from 'next/link';

interface IProps {
    items: Article[];
}

export default function BlogShortList({ items }: Readonly<IProps>) {
    return isEmpty(items) ? (
        <></>
    ) : (
        <div className="rbt-single-widget rbt-widget-recent">
            <div className="inner">
                <h4 className="rbt-widget-title">Tin tức mới nhất</h4>
                <ul className="rbt-sidebar-list-wrapper recent-post-list">
                    {items.map((item) => (
                        <li key={item.id}>
                            <div className="thumbnail">
                                <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>
                                    <img src={`${API_URL}${item.image.url}`} alt={item.title} />
                                </Link>
                            </div>
                            <div className="content">
                                <h6 className="title">
                                    <Link href={`${ARTICLE_ROUTE}/${item.slug}-${item.documentId}`}>{item.title}</Link>
                                </h6>
                                <ul className="rbt-meta">
                                    <li>
                                        <i className="feather-clock" />
                                        {formatDateTime(item.publishedAt)}
                                    </li>
                                </ul>
                            </div>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    );
}

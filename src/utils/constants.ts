export enum AUTH_KEYS {
    //ACCESS_TOKEN = 'access_token',
    //REFRESH_TOKEN = 'refresh_token',
    USER_COOKIE = 'currentUser',
    HEADER_PATHNAME = 'x-pathname',
    HEADER_ACCESS_TOKEN = 'x-access_token',
    HEADER_USER_ID = 'x-user_id',
    //LANGUAGE_CODE = 'lang-code',
    CART = 'cart',
    CART_PRODUCT = 'cart_product',
}

export enum COMMON_MESSAGE {
    ERROR_MESSAGE = 'Có lỗi xảy ra trong quá trình thực hiện',
    DELETE_CART = 'Đã xoá khỏi giỏ hàng',
    FIELD_REQUIRED = 'Trường này không được bỏ trống',
    FIELD_PASSWORD_LENGTH = 'Mật khẩu ít nhất 6 ký tự',
    FIELD_PASSWORD_MATCH = 'Mật khẩu không giống nhau',
    FIELD_EMAIL = 'Email không hợp lệ',
}

export const API_URL = process.env.NEXT_PUBLIC_API_URL ?? 'https://hocvienyeu.vn';
export const WEB_URL = process.env.NEXT_PUBLIC_WEB_URL ?? 'https://hocvienyeu.vn';
export const EMAIL_HOST = process.env.NEXT_PUBLIC_EMAIL_HOST ?? 'smtp.gmail.com';
export const EMAIL_PORT = Number(process.env.NEXT_PUBLIC_EMAIL_PORT ?? 587);
export const EMAIL_FROM = process.env.NEXT_PUBLIC_EMAIL_FROM ?? '<EMAIL>';
export const EMAIL_PASSWORD = process.env.NEXT_PUBLIC_EMAIL_PASSWORD ?? '';
export const GG_RECAPTCHA_KEY = process.env.NEXT_PUBLIC_GG_RECAPTCHA_KEY ?? '6LeIZN8qAAAAAFPlKDqkUAUqgQ5nB0fA1pXFO4Bp';
export const API_BEARER_READ = process.env.NEXT_PUBLIC_API_BEARER_READ ?? '';
export const API_BEARER_MUTATION = process.env.NEXT_PUBLIC_API_BEARER_MUTATION ?? '';
export const API_KEY_VDO = process.env.NEXT_PUBLIC_API_KEY_VDO ?? '';
export const API_VIETQR_URL = process.env.NEXT_PUBLIC_API_VIETQR_URL ?? 'https://api.vietqr.io/v2/generate';
export const API_VIETQR_CLIENT = process.env.NEXT_PUBLIC_API_VIETQR_CLIENT ?? '';
export const API_VIETQR_KEY = process.env.NEXT_PUBLIC_API_VIETQR_KEY ?? '';

export const ARTICLE_LIMIT = 4;
export const COURSE_LIMIT = 12;

export const ARTICLE_ROUTE = '/tin-tuc';
export const COURSE_ROUTE = '/khoa-hoc';
export const PRODUCT_ROUTE = '/san-pham';

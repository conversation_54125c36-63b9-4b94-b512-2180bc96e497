import { BaseSearch } from '@/types/common';
import { SearchCourse } from '@/types/Course';
import { ToastOptions, toast } from 'react-toastify';
import { COURSE_ROUTE, PRODUCT_ROUTE } from './constants';
import { SearchProduct } from '@/types/Product';

export const formatNumber = (value: number) => new Intl.NumberFormat('de-DE').format(value);

const getSearchParams = (searchParams: BaseSearch) => {
    const params = new URLSearchParams({});
    if (searchParams.q) {
        params.append('q', searchParams.q);
    }
    if (searchParams.tagId && searchParams.tagId > 0) {
        params.append('tId', searchParams.tagId.toString());
        params.append('slug', searchParams.slug ?? '');
    }
    if (searchParams.sortBy && searchParams.sortBy > 0) {
        params.append('sortBy', searchParams.sortBy.toString());
    }
    if (searchParams.page) {
        params.append('page', searchParams.page.toString());
    }
    return params;
};

export const getSearchRoute = (baseUrl: string, searchParams: BaseSearch) =>
    `${baseUrl}?${getSearchParams(searchParams).toString()}`;

export const getSearchCourseRoute = (searchParams: SearchCourse) => {
    const params = getSearchParams(searchParams);
    if (searchParams.categoryId && searchParams.categoryId > 0) {
        params.append('cId', searchParams.categoryId.toString());
        params.append('slug', searchParams.slug ?? '');
    }
    if (searchParams.highlight) {
        params.append('highlight', '1');
    }
    if (searchParams.isOffline && searchParams.isOffline !== '0') {
        params.append('isOffline', searchParams.isOffline);
    }
    if ((searchParams.priceRange ?? -1) >= 0) {
        params.append('priceRange', searchParams.priceRange!.toString());
    }
    if (searchParams.displayList) {
        params.append('displayList', '1');
    }
    return `${COURSE_ROUTE}?${params.toString()}`;
};

export const getSearchProductRoute = (searchParams: SearchProduct) => {
    const params = getSearchParams(searchParams);
    if (searchParams.categoryId && searchParams.categoryId > 0) {
        params.append('cId', searchParams.categoryId.toString());
        params.append('slug', searchParams.slug ?? '');
    }
    if ((searchParams.priceRange ?? -1) >= 0) {
        params.append('priceRange', searchParams.priceRange!.toString());
    }
    return `${PRODUCT_ROUTE}?${params.toString()}`;
};

export const showToast = (success: boolean, message: string) => {
    const options: ToastOptions<object> = {
        position: 'top-right',
        toastId: Math.random(),
    };
    if (success) toast.success(message, options);
    else toast.error(message, options);
};

export const randomNumber = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1) + min);

export const isAuthRoute = (pathname: string) =>
    pathname.startsWith('/hoc-bai') ||
    pathname.startsWith('/dashboard') ||
    pathname.startsWith('/profile') ||
    pathname.startsWith('/khoa-hoc-dang-ky') ||
    pathname.startsWith('/bookmark') ||
    pathname.startsWith('/don-hang');

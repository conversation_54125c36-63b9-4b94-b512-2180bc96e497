/*---------------------------
    Header Common Styles  
----------------------------*/
.logo {
    a {
        display: block;

        img {
            max-height: 50px;
            object-fit: cover;
        }
    }
}

.rbt-round-btn {
    width: 40px;
    height: 40px;
    line-height: 41px;
    text-align: center;
    border-radius: 100%;
    position: relative;
    overflow: hidden;
    z-index: 1;
    background: transparent;
    padding: 0;
    border: 0 none;
    i {
        margin-right: 0;
    }
    &::after {
        background: var(--color-gray-light);
        position: absolute;
        content: "";
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        transition: 0.4s;
        opacity: 0;
        transform: scale(0.8);
        border-radius: 100%;
        z-index: -1;
    }
    &.btn-white-off {
        color: var(--color-white-off);
    }
    &:hover,
    &.open {
        color: var(--color-primary);
        &::after {
            opacity: 1;
            transform: scale(1);
        }
    }
}


.rbt-information-list {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-left: 0;
    list-style: none;
    margin: -2px -7px;
    flex-wrap: wrap;
    li {
        margin: 2px 7px;
        color: var(--color-body);
        a {
            padding: 0;
            font-size: 14px;
            line-height: 22px;
            color: var(--color-body);
            display: flex;
            align-items: center;
            transition: 0.4s;
            i {
                margin-right: 5px;
                font-size: 14px;
                margin-top: 2px;
            }
            &:hover {
                color: var(--color-primary);
            }
        }
    }
}

/* For Header Sticky  */
@keyframes headerSticky {
    0% {
        transform: translateY(-100%);
    }

    100% {
        transform: translateY(0);
    }
}

@keyframes stickySlideDown {
    0% {
        transform: translateY(-100%);
    }

    to {
        transform: translateY(0);
    }
}
















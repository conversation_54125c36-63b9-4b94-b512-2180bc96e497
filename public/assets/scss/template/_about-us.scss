.rbt-about-page{
    .about-page-title{
        position: relative;
        &::before{
            content: '';
            position: absolute;
            bottom: 67px;
            left: 167px;
            background: url(../images/about-img/shape/shape-3.png);
            background-size: cover;
            height: 92px;
            width: 92px;
        }
        &::after{
            content: '';
            position: absolute;
            bottom: 63px;
            right: 312px;
            background: url(../images/about-img/shape/shape-4.png);
            background-size: cover;
            height: 51px;
            width: 51px;
        }
        .section-title{
            display: flex;
            flex-direction: column;
            align-items: center;
            .title{
                max-width: 821px;
                margin-bottom: 15px;
            }
            .inner-p{
                max-width: 658px;
            }
        }
    }
    .about-content{
        padding-bottom: 90px;
        display: flex;
        align-items: center;
        .inner-img{
            position: relative;
            z-index: 2;
            &::before{
                content: '';
                position: absolute;
                bottom: -60px;
                left: -43px;
                background: url(../images/about-img/shape/shape-2.png);
                background-size: cover;
                height: 87px;
                width: 95px;
                z-index: -1;
            }
            img{
                border-radius: 6px;
            }
        }
        .inner-content{
            position: relative;
            &::after{
                content: '';
                position: absolute;
                bottom: 0;
                right: -150px;
                background: url(../images/about-img/shape/shape-5.png);
                background-size: cover;
                height: 175px;
                width: 150px;
            }
            .section-title{
                .title-badge{
                    margin-bottom: 5px;
                }
                .title{
                    font-size: 48px;
                    line-height: 60px;
                    max-width: 383px;
                    margin-bottom: 15px;
                    span{
                        color: var(--color-primary);
                    }
                }
                .inner-p{
                    margin-bottom: 25px;
                }
            }
            .inner-badge{
                display: inline-block;
                padding: 15px 35px;
                background: #F8FAFE;
                border-radius: 4px;
                transition: 0.3s;
                &.inner-badge-orange{
                    background: #FEFDF8;
                    a{
                        color: #F2994A;
                    }
                }
                &.inner-badge-red{
                    background: #FFF9F9;
                    a{
                        color: #F96F6F;
                    }
                }
                &.inner-badge-green{
                    background: #F5FCFA;
                    a{
                        color: #148C8A;
                    }
                }
                a{
                    font-size: var(--font-size-b3);
                    color: #2D9CDB;
                    display: flex;
                }
            }
            .inner-badge+.inner-badge{
                margin-left: 10px;
                margin-top: 10px;
            }
        }
    }
    .about-vedio-content{
        .inner-content{
            .section-title{
                .title-badge{
                    margin-bottom: 5px;
                }
                .title{
                    font-size: 48px;
                    line-height: 60px;
                    max-width: 467px;
                    margin-bottom: 15px;
                    span{
                        color: var(--color-secondary);
                    }
                }
                .inner-p{
                    margin-bottom: 15px;
                    max-width: 500px;
                }
            }
            a{
                &.rbt-btn{
                    display: flex;
                    color: var(--color-primary);
                }
            }
        }
        .embed-responsive{
            position: relative;
            z-index: 2;
            &::after{
                content: '';
                position: absolute;
                bottom: -40px;
                right: -43px;
                background: url(../images/about-img/shape/shape-2.png);
                background-size: cover;
                height: 87px;
                width: 95px;
                z-index: -1;
            }
            iframe{
                width: 100%;
                height: 400px;
                border-radius: 6px;
            }
        }
    }
    .rbt-counterup-area{
        background-color: var(--color-card-4);
    }
    .teacher-section-area{
        background: var(--color-light);
    }
}
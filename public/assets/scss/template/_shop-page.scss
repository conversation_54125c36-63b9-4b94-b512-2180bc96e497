.rbt-shop-page{
    background: var(--color-white);
    .search-section{
        margin-bottom: 40px;
        .search-box{
            background: var(--color-white);
            box-shadow: -8px 6px 50px rgba(25, 35, 53, 0.08);
            border-radius: 4px;
            padding: 23px 20px;
            display: flex;
            justify-content: space-between;
            input{
                border: none;
                font-size: 14px;
                line-height: 15px;
                color: #A1A9AC;
                outline: none;
                width: 80%;
                &::placeholder{
                    font-size: 14px;
                    line-height: 15px;
                    color: #A1A9AC;
                }
            }
            button{
                border: none;
                font-size: 14px;
                line-height: 15px;
                color: #A1A9AC;
                background: none;
                padding: 0;
                &:hover{
                    color: var(--color-secondary);
                }
            }
        }
    }
    .filter-select-option {
        flex-basis: 20%;
        padding: 10px;
        
        @media #{$lg-layout} {
            flex-basis: 33.33%;
        }

        @media #{$md-layout} {
            flex-basis: 50%;
        }

        @media #{$sm-layout} {
            flex-basis: 50%;
        }

        @media #{$large-mobile} {
            flex-basis: 100%;
        }

        .filter-leble {
            display: block;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 10px;
        }
        .nice-select {
            width: 100%;
            padding-left: 20px;
            padding-right: 40px;
            &::after {
                right: 22px;
                height: 8px;
                width: 8px;
                opacity: 0.5;
            }
            .list {
                min-width: 100%;
                max-height: 300px;
                overflow-y: auto;
            }
        }
    }
    .popular-product-part{
        margin-bottom: 40px;
        .list-style-product-card{
            padding: 18px 0;
            border-bottom: 1px solid #DCE4EE;
            &:first-child{
                padding-top: 0;
            }
            &:last-child{
                padding-bottom: 0;
                border: none;
            }
        }
    }
    .product-tag-section{
        .tag-part{
            a{
                background: #F5F7FA;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 12px;
                line-height: 12px;
                color: var(--color-heading);
                display: inline-block;
                margin-right: 10px;
                margin-top: 10px;
                transition: 0.4s;
                &:hover{
                    background: var(--color-secondary-alt);
                    color: var(--color-secondary);
                }
            }
        }
    }
    .shorting-section{
        display: flex;
        justify-content: space-between;
        align-items: center;
        p{
            margin-bottom: 0;
            color: var(--color-heading);
        }
        .shorting-part{
            padding: 20px;
            background: var(--color-secondary);
            border-radius: 6px;
            font-size: var(--font-size-b3);
            width: 215px;
            select{
                background: none;
                border: none;
                color: var(--color-white);
                width: 100%;
                outline: none;
                option{
                    padding: 20px;
                    color: var(--color-heading);
                    width: 100%;
                    border: none;
                }
            }
        }
    }
    .rbt-book-card{
        .card-img{
            position: relative;
            span{
                &.img-tag{
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    background: var(--color-primary);
                    padding: 6px 10px;
                    color: var(--color-white);
                    font-size: 14px;
                    line-height: 16px;
                    font-weight: 700;
                    border-radius: 2px;
                    border: none;
                }
            }
        }
    }
}
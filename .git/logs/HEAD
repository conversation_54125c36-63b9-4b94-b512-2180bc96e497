0000000000000000000000000000000000000000 5d819f6157edb1670a9afea07e4fc87d88ecd92b Hoàn XPROZ <<EMAIL>> 1753116117 +0700	clone: from https://github.com/hoanmuada/hocvienyeu-fe.git
5d819f6157edb1670a9afea07e4fc87d88ecd92b 5d819f6157edb1670a9afea07e4fc87d88ecd92b Hoàn XPROZ <<EMAIL>> 1753117866 +0700	checkout: moving from main to fix/object_relation
5d819f6157edb1670a9afea07e4fc87d88ecd92b 0c384161b3e1d1eacc2521fa5a70a64ff98e38a0 Hoàn XPROZ <<EMAIL>> 1753117883 +0700	commit: fix
0c384161b3e1d1eacc2521fa5a70a64ff98e38a0 7b58dbdf0f6de478e7cd041b1b8efb89fd1b2a0a Hoàn XPROZ <<EMAIL>> 1753119224 +0700	commit: refactor: update Order interface to use user_id instead of user
7b58dbdf0f6de478e7cd041b1b8efb89fd1b2a0a 112c7e03c4378413daf7ad10570d78c0eeacfcc2 Hoàn XPROZ <<EMAIL>> 1753189045 +0700	merge main: Fast-forward
112c7e03c4378413daf7ad10570d78c0eeacfcc2 a4bdd9d41aa9a3c974820de3a4d89a9a06cde51a Hoàn XPROZ <<EMAIL>> 1753192802 +0700	commit: fix: change userId and courseId types from string to number across multiple files
a4bdd9d41aa9a3c974820de3a4d89a9a06cde51a 112c7e03c4378413daf7ad10570d78c0eeacfcc2 Hoàn XPROZ <<EMAIL>> 1753193757 +0700	checkout: moving from fix/object_relation to main
112c7e03c4378413daf7ad10570d78c0eeacfcc2 1ed9abf87d6c5158c7798d867ba1106b1d37d6b1 Hoàn XPROZ <<EMAIL>> 1753193765 +0700	pull --ff --recurse-submodules --progress origin: Fast-forward
1ed9abf87d6c5158c7798d867ba1106b1d37d6b1 621d37cc9695028489bc13b0ea1e6f24f51d69b7 Hoàn XPROZ <<EMAIL>> 1754753163 +0700	commit: feat: enhance order display with shipping details and sorting
621d37cc9695028489bc13b0ea1e6f24f51d69b7 e97a5a769ce1cdb741b7f199236250d021fd987a Hoàn XPROZ <<EMAIL>> 1755168039 +0700	commit: feat: implement client IP retrieval and update userCourses fetching logic
e97a5a769ce1cdb741b7f199236250d021fd987a 0779f2f8a0e33cc8e9d52e200a7e95b69daeed5e Hoàn XPROZ <<EMAIL>> 1755168477 +0700	commit: feat: enhance IP retrieval logic to filter out local addresses
0779f2f8a0e33cc8e9d52e200a7e95b69daeed5e 1635d8630014f12971a741dc43101588b8891aee Hoàn XPROZ <<EMAIL>> 1755168768 +0700	commit: feat: enhance IP retrieval logic to filter out local addresses
1635d8630014f12971a741dc43101588b8891aee 17146d65c36f7f0ca0572e0fa097314dbc7f8fcb Hoàn XPROZ <<EMAIL>> 1755169012 +0700	commit: feat: enhance IP retrieval logic to filter out local addresses
17146d65c36f7f0ca0572e0fa097314dbc7f8fcb 3d28e2554d013107967e30c19d578387123c3a3e Hoàn XPROZ <<EMAIL>> 1755169780 +0700	commit: feat: improve client IP retrieval debugging by logging all headers
3d28e2554d013107967e30c19d578387123c3a3e 4eb2a01235db8a9e3f404b1b72d7895c6623b982 Hoàn XPROZ <<EMAIL>> 1755261116 +0700	commit: feat: implement local login functionality and enhance IP logging in login handler
4eb2a01235db8a9e3f404b1b72d7895c6623b982 112f28662f6b4c7242cffec5f2e9a18dcaf72429 Hoàn XPROZ <<EMAIL>> 1755264314 +0700	commit: feat: implement user login tracking with IP logging and CRUD operations for user logins
112f28662f6b4c7242cffec5f2e9a18dcaf72429 1ce7b2f03d1bb5d4d4eccbabb35f348a0edfeb65 Hoàn XPROZ <<EMAIL>> 1755268030 +0700	commit: feat: implement local logout functionality with API integration and error handling
1ce7b2f03d1bb5d4d4eccbabb35f348a0edfeb65 bcf239a895118b6a9617fb8ec169531bb0f1a446 Hoàn XPROZ <<EMAIL>> 1755269329 +0700	commit: feat: refactor IP retrieval logic to improve accuracy and simplify code
bcf239a895118b6a9617fb8ec169531bb0f1a446 d2fb2e35792246c6ab463383b56ca0409ff56120 Hoàn XPROZ <<EMAIL>> 1755272140 +0700	commit: feat: enhance user login validation with access token and IP checks

{"name": "hocvienyeu-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write \"src/**/*.{js,jsx,tsx,ts}\""}, "dependencies": {"@google-recaptcha/react": "^1.2.3", "@hookform/resolvers": "^3.9.0", "@material-ui/core": "^4.12.4", "add": "^2.0.6", "classnames": "^2.5.1", "date-fns": "^4.1.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "marked": "^14.1.3", "next": "14.2.13", "nodemailer": "^6.10.0", "qs": "^6.13.0", "react": "^18", "react-countup": "^6.5.3", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-modal-video": "^2.0.2", "react-toastify": "^11.0.3", "react-visibility-sensor": "^5.1.1", "sass": "^1.79.3", "swiper": "^11.1.14", "usehooks-ts": "^3.1.0", "yarn": "^1.22.22", "yup": "^1.4.0", "zustand": "^5.0.3"}, "devDependencies": {"@redux-devtools/extension": "^3.3.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.9", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/qs": "^6.9.16", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal-video": "^1.2.3", "eslint": "^8", "eslint-config-next": "14.2.13", "prettier": "^3.3.3", "typescript": "^5"}}